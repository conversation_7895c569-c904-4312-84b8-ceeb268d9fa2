import os
import secrets
from typing import Optional, Dict, Any, List
from pydantic_settings import BaseSettings
from pydantic import validator
import json


class Settings(BaseSettings):
    """Application settings."""

    # Base settings
    PROJECT_NAME: str = "TransMediaX"
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)

    # Database settings
    DATABASE_PATH: str = "data/database.sqlite"
    SQL_ECHO: bool = False

    # CORS settings
    BACKEND_CORS_ORIGINS: List[str] = ["*"]

    # File storage settings
    MEDIA_STORAGE_PATH: str = "data/media"

    # Authentication settings
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days

    # Admin settings
    ADMIN_USERNAME: str = "admin"
    ADMIN_PASSWORD: str = "admin"  # Change this in production

    # Service settings
    # Speech to Text service (OpenAI compatible)
    STT_API_KEY: Optional[str] = None
    STT_BASE_URL: str = "https://api.openai.com/v1"
    STT_MODEL: str = "whisper-1"

    # Text to Speech service (OpenAI compatible)
    TTS_API_KEY: Optional[str] = None
    TTS_BASE_URL: str = "https://api.openai.com/v1"
    TTS_MODEL: str = "tts-1"

    # LLM service (OpenAI compatible)
    LLM_API_KEY: Optional[str] = None
    LLM_BASE_URL: str = "https://api.openai.com/v1"
    LLM_MODEL: str = "gpt-3.5-turbo"

    # 兼容旧版本配置
    AUDIO_API_KEY: Optional[str] = None
    AUDIO_BASE_URL: str = "https://api.openai.com/v1"

    # Image generation service
    COMFYUI_BASE_URL: str = "http://127.0.0.1:8188"

    # Config file path
    CONFIG_FILE_PATH: str = "data/config.json"

    @validator("DATABASE_PATH", pre=True)
    def assemble_db_path(cls, v: Optional[str], values: Dict[str, Any]) -> str:
        """Assemble database path."""
        if v and os.path.isabs(v):
            return v
        return os.path.join(os.getcwd(), v or "data/database.sqlite")

    @validator("MEDIA_STORAGE_PATH", pre=True)
    def assemble_media_path(cls, v: Optional[str], values: Dict[str, Any]) -> str:
        """Assemble media storage path."""
        if v and os.path.isabs(v):
            return v
        return os.path.join(os.getcwd(), v or "data/media")

    def load_from_file(self):
        """Load settings from config file."""
        if not os.path.exists(self.CONFIG_FILE_PATH):
            return

        try:
            with open(self.CONFIG_FILE_PATH, "r") as f:
                config_data = json.load(f)

            for key, value in config_data.items():
                if hasattr(self, key):
                    setattr(self, key, value)
        except Exception as e:
            print(f"Error loading config file: {e}")

    def save_to_file(self):
        """Save settings to config file."""
        # Ensure the directory exists
        os.makedirs(os.path.dirname(self.CONFIG_FILE_PATH), exist_ok=True)

        # Get all settings as dict
        settings_dict = self.dict()

        # Remove sensitive settings
        settings_dict.pop("SECRET_KEY", None)

        try:
            with open(self.CONFIG_FILE_PATH, "w") as f:
                json.dump(settings_dict, f, indent=2)
        except Exception as e:
            print(f"Error saving config file: {e}")


# Create settings instance
settings = Settings()

# Load settings from file
settings.load_from_file()
