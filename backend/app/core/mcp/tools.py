"""MCP 工具生成器

该模块负责根据转换器自动生成 MCP 工具。
"""

import logging
from typing import Dict, Any, List, Optional, Type
from fastmcp import FastMCP, Context
import uuid
import asyncio

from ..converters.base import AbstractConverter, ConversionTask, ConverterRegistry
from ..media.base import AbstractMedia
from ..media.text import TextMedia
from ..media.image import ImageMedia
from ..media.audio import AudioMedia

logger = logging.getLogger(__name__)


class ConverterToolGenerator:
    """转换器工具生成器，为每个转换器自动生成 MCP 工具"""

    def __init__(self, mcp_server: FastMCP):
        self.mcp_server = mcp_server
        self.registered_converters: Dict[str, AbstractConverter] = {}

    def register_converter_tools(self):
        """注册所有转换器的 MCP 工具"""
        # 获取所有已注册的转换器
        converters = ConverterRegistry.get_all()

        # 清除之前注册的工具（如果有的话）
        self._clear_previous_tools()

        # 为每个转换器注册工具
        for converter in converters:
            self._register_converter_tool(converter)

        logger.info(f"已注册 {len(converters)} 个转换器的 MCP 工具")

    def _clear_previous_tools(self):
        """清除之前注册的转换器工具"""
        # 注意：FastMCP 可能没有直接的清除工具方法，这里我们记录已注册的转换器
        self.registered_converters.clear()

    def _register_converter_tool(self, converter: AbstractConverter):
        """为单个转换器注册 MCP 工具"""
        converter_name = converter.name.lower().replace(" ", "_")
        tool_name = f"convert_{converter_name}"

        # 生成工具描述
        input_types_str = ", ".join([t.__name__ for t in converter.input_types])
        output_types_str = ", ".join([t.__name__ for t in converter.output_types])

        description = (
            f"{converter.description}\n"
            f"输入类型: {input_types_str}\n"
            f"输出类型: {output_types_str}"
        )

        # 动态创建工具函数
        async def converter_tool(
            input_content: str,
            parameters: Optional[Dict[str, Any]] = None,
            ctx: Context = None
        ) -> Dict[str, Any]:
            """
            执行媒体转换

            Args:
                input_content: 输入内容（文本、文件路径等）
                parameters: 转换参数（可选）
                ctx: MCP 上下文

            Returns:
                转换结果
            """
            if ctx:
                await ctx.info(f"开始执行 {converter.name} 转换")

            try:
                # 创建转换任务
                task = ConversionTask(
                    parameters=parameters or {},
                    user_id="mcp_user"
                )

                # 根据转换器的输入类型创建输入媒体
                input_media = await self._create_input_media(
                    converter.input_types[0],
                    input_content
                )

                if ctx:
                    await ctx.info(f"正在执行转换...")

                # 执行转换
                output_media = converter.convert(task, [input_media])

                if not output_media:
                    raise ValueError("转换失败，没有生成输出")

                # 处理输出结果
                result = await self._process_output_media(output_media[0], ctx)

                if ctx:
                    await ctx.info(f"转换完成")

                return {
                    "success": True,
                    "task_id": task.id,
                    "result": result,
                    "output_type": type(output_media[0]).__name__
                }

            except Exception as e:
                error_msg = f"转换失败: {str(e)}"
                if ctx:
                    await ctx.error(error_msg)
                logger.exception(f"转换器 {converter.name} 执行失败")
                return {
                    "success": False,
                    "error": error_msg
                }

        # 设置工具函数的名称和文档
        converter_tool.__name__ = tool_name
        converter_tool.__doc__ = description

        # 注册工具到 MCP 服务器
        self.mcp_server.tool()(converter_tool)

        # 记录已注册的转换器
        self.registered_converters[tool_name] = converter

        logger.info(f"已注册 MCP 工具: {tool_name}")

    async def _create_input_media(self, media_type: Type[AbstractMedia], content: str) -> AbstractMedia:
        """根据媒体类型创建输入媒体对象"""
        if media_type == TextMedia:
            return TextMedia(content=content)
        elif media_type == ImageMedia:
            # 假设 content 是图像文件路径或 base64 数据
            media = ImageMedia()
            media.load(content)
            return media
        elif media_type == AudioMedia:
            # 假设 content 是音频文件路径
            media = AudioMedia()
            media.load(content)
            return media
        else:
            raise ValueError(f"不支持的输入媒体类型: {media_type}")

    async def _process_output_media(self, media: AbstractMedia, ctx: Context = None) -> Dict[str, Any]:
        """处理输出媒体，返回适合 MCP 的格式"""
        if isinstance(media, TextMedia):
            metadata_obj = media.get_metadata()
            return {
                "type": "text",
                "content": media.content,
                "metadata": getattr(metadata_obj, 'metadata', getattr(metadata_obj, 'metadatas', {}))
            }
        elif isinstance(media, ImageMedia):
            # 对于图像，我们可能需要返回 base64 编码或文件路径
            metadata_obj = media.get_metadata()
            return {
                "type": "image",
                "file_path": metadata_obj.file_path,
                "metadata": getattr(metadata_obj, 'metadata', getattr(metadata_obj, 'metadatas', {}))
            }
        elif isinstance(media, AudioMedia):
            # 对于音频，返回文件路径和元数据
            metadata_obj = media.get_metadata()
            return {
                "type": "audio",
                "file_path": metadata_obj.file_path,
                "metadata": getattr(metadata_obj, 'metadata', getattr(metadata_obj, 'metadatas', {}))
            }
        else:
            return {
                "type": "unknown",
                "content": str(media),
                "metadata": {}
            }


def create_converter_status_tool(mcp_server: FastMCP):
    """创建转换器状态查询工具"""

    @mcp_server.tool()
    async def list_available_converters(ctx: Context = None) -> Dict[str, Any]:
        """列出所有可用的转换器及其信息"""
        if ctx:
            await ctx.info("正在获取可用转换器列表...")

        converters = ConverterRegistry.get_all()
        converter_info = []

        for converter in converters:
            info = {
                "name": converter.name,
                "description": converter.description,
                "input_types": [t.__name__ for t in converter.input_types],
                "output_types": [t.__name__ for t in converter.output_types]
            }
            converter_info.append(info)

        return {
            "total_converters": len(converter_info),
            "converters": converter_info
        }

    @mcp_server.tool()
    async def get_converter_info(converter_name: str, ctx: Context = None) -> Dict[str, Any]:
        """获取特定转换器的详细信息"""
        if ctx:
            await ctx.info(f"正在获取转换器 {converter_name} 的信息...")

        converters = ConverterRegistry.get_all()

        for converter in converters:
            if converter.name.lower().replace(" ", "_") == converter_name.lower():
                return {
                    "name": converter.name,
                    "description": converter.description,
                    "input_types": [t.__name__ for t in converter.input_types],
                    "output_types": [t.__name__ for t in converter.output_types]
                }

        return {
            "error": f"未找到转换器: {converter_name}"
        }
