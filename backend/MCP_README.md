# TransMediaX MCP 服务器

TransMediaX 现在支持 Model Context Protocol (MCP) 服务器功能，可以将多媒体转换功能作为 MCP 工具提供给 LLM 应用程序使用。

## 功能特性

- **自动工具生成**: 根据 `backend/app/core/converters` 目录中的转换器自动生成 MCP 工具
- **动态更新**: 当转换器发生变化时，MCP 工具会自动更新
- **多种传输方式**: 支持 STDIO、SSE 和 Streamable HTTP 传输
- **实时监控**: 可选的转换器变化监控功能
- **完整集成**: 与现有 FastAPI 应用无缝集成

## 配置

### 基本配置

MCP 服务器的基本配置在 `backend/app/config.py` 中：

```python
# MCP Server settings
MCP_ENABLED: bool = True                    # 是否启用 MCP 服务器
MCP_SERVER_NAME: str = "TransMediaX MCP Server"  # 服务器名称
MCP_TRANSPORT: str = "sse"                  # 传输方式: stdio, sse, streamable-http
MCP_HOST: str = "127.0.0.1"                # 服务器主机
MCP_PORT: int = 8001                       # 服务器端口
MCP_PATH: str = "/mcp"                     # MCP 路径
MCP_MONITORING_ENABLED: bool = True        # 是否启用监控
MCP_MONITORING_INTERVAL: int = 30          # 监控间隔（秒）
```

### 全局转换器配置

转换器参数通过全局配置管理，同时用于 API 和 MCP：

```python
# Global Converter Configuration (used by both API and MCP)
# Text-to-Speech (TTS) Settings
TTS_AVAILABLE_VOICES: List[str] = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
TTS_AVAILABLE_MODELS: List[str] = ["F5-TTS"]
TTS_AVAILABLE_FORMATS: List[str] = ["mp3", "opus", "aac", "flac"]
TTS_DEFAULT_VOICE: str = "alloy"
TTS_DEFAULT_MODEL: str = "F5-TTS"
TTS_DEFAULT_FORMAT: str = "mp3"
TTS_SPEED_MIN: float = 0.25
TTS_SPEED_MAX: float = 4.0
TTS_SPEED_DEFAULT: float = 1.0

# Speech-to-Text (STT) Settings
STT_AVAILABLE_MODELS: List[str] = ["SenseVoiceSmall"]
STT_AVAILABLE_RESPONSE_FORMATS: List[str] = ["json", "text", "srt", "verbose_json", "vtt"]
STT_DEFAULT_MODEL: str = "SenseVoiceSmall"
STT_DEFAULT_RESPONSE_FORMAT: str = "text"

# Image Generation Settings
IMAGE_SIZE_MIN: int = 64
IMAGE_SIZE_MAX: int = 2048
IMAGE_SIZE_DEFAULT: int = 512
IMAGE_COUNT_MAX: int = 4
IMAGE_COUNT_DEFAULT: int = 1
IMAGE_CFG_SCALE_DEFAULT: float = 7.5
IMAGE_STEPS_DEFAULT: int = 20
# ... 更多参数配置
```

### Admin 界面配置

1. 访问 `http://localhost:8000/admin.html`
2. 登录管理员账户
3. 在 "MCP Tool Configuration" 部分修改参数
4. 点击 "Save MCP Configuration" 保存
5. MCP 工具会自动刷新，使用新的参数配置

## 使用方法

### 1. 与 FastAPI 集成运行

启动 FastAPI 应用时，MCP 服务器会自动在后台线程中启动（除非配置为 STDIO 模式）：

```bash
cd backend
python -m app.main
```

### 2. 独立运行 MCP 服务器

使用提供的独立脚本：

```bash
cd backend

# STDIO 模式（默认）
python mcp_server.py

# SSE 模式
python mcp_server.py --transport sse --host 127.0.0.1 --port 8001

# Streamable HTTP 模式
python mcp_server.py --transport streamable-http --host 127.0.0.1 --port 8001

# 查看所有选项
python mcp_server.py --help
```

### 3. 测试 MCP 服务器

使用测试客户端验证功能：

```bash
cd backend

# 测试内存模式（直接连接）
python test_mcp_client.py --transport memory

# 测试 SSE 模式
python test_mcp_client.py --transport sse --url http://127.0.0.1:8001/mcp

# 测试 STDIO 模式
python test_mcp_client.py --transport stdio --server-script mcp_server.py
```

## 可用的 MCP 工具

MCP 服务器会自动为每个转换器生成对应的工具：

### 转换器工具

- `text_to_image_converter`: 文本到图像转换
  - **输入参数**：
    - `text` (str): 要转换为图像的文本描述
    - `parameters` (TextToImageParameters, 可选): 转换参数
      - `width` (int): 图像宽度（64-2048，默认512）
      - `height` (int): 图像高度（64-2048，默认512）
      - `num_images` (int): 生成图像数量（1-4，默认1）
      - `positive_prompt` (str, 可选): 正向提示词
      - `negative_prompt` (str, 可选): 负向提示词
      - `steps` (int, 可选): 推理步数（1-100）
      - `cfg_scale` (float, 可选): CFG 引导强度（1.0-20.0）
      - `seed` (int, 可选): 随机种子

- `text_to_audio_converter`: 文本到音频转换
  - **输入参数**：
    - `text` (str): 要转换为音频的文本内容
    - `parameters` (TextToAudioParameters, 可选): 转换参数
      - `voice` (str): 语音类型（alloy, echo, fable, onyx, nova, shimmer）
      - `speed` (float): 语音速度（0.25-4.0，默认1.0）
      - `model` (str): TTS 模型（tts-1, tts-1-hd）
      - `format` (str): 音频格式（mp3, opus, aac, flac）

- `audio_to_text_converter`: 音频到文本转换
  - **输入参数**：
    - `audio_file_path` (str): 音频文件路径或 URL
    - `parameters` (AudioToTextParameters, 可选): 转换参数
      - `model` (str): ASR 模型（whisper-1）
      - `language` (str, 可选): 音频语言（ISO 639-1 代码）
      - `prompt` (str, 可选): 提示文本
      - `response_format` (str): 响应格式（json, text, srt, verbose_json, vtt）
      - `temperature` (float): 采样温度（0.0-1.0）

### 系统工具

- `get_server_info`: 获取服务器信息
- `health_check`: 健康检查
- `list_available_converters`: 列出所有可用转换器
- `get_converter_info`: 获取特定转换器信息

## 工具使用示例

### 文本到图像转换

```python
# 基本用法
result = await client.call_tool("text_to_image_converter", {
    "text": "一只可爱的小猫坐在花园里"
})

# 带详细参数的用法
result = await client.call_tool("text_to_image_converter", {
    "text": "美丽的风景画",
    "parameters": {
        "width": 1024,
        "height": 768,
        "num_images": 2,
        "positive_prompt": "beautiful landscape, high quality, detailed",
        "negative_prompt": "blurry, low quality, distorted",
        "steps": 50,
        "cfg_scale": 7.5,
        "seed": 12345
    }
})
```

### 文本到音频转换

```python
# 基本用法
result = await client.call_tool("text_to_audio_converter", {
    "text": "你好，欢迎使用 TransMediaX"
})

# 带详细参数的用法
result = await client.call_tool("text_to_audio_converter", {
    "text": "这是一段测试音频，使用了自定义参数。",
    "parameters": {
        "voice": "nova",
        "speed": 1.2,
        "model": "tts-1-hd",
        "format": "mp3"
    }
})

# 返回结果示例：
{
  "success": true,
  "task_id": "941bf513-96f5-4b1a-b2d4-b1bf95a499bc",
  "result": {
    "id": "253fc8a5-8b6b-45b5-8e56-aebe457e6c76",
    "type": "audio",
    "file_url": "http://localhost:8000/api/v1/media/253fc8a5-8b6b-45b5-8e56-aebe457e6c76/file",
    "preview_url": "data:audio/mp3;base64,//OExAAAAA...",
    "metadata": {
      "model": "F5-TTS",
      "voice": "alloy",
      "format": "mp3",
      "speed": 1.0
    },
    "content_type": "audio"
  },
  "output_type": "AudioMedia",
  "media_ids": ["253fc8a5-8b6b-45b5-8e56-aebe457e6c76"]
}
```

### 音频到文本转换

```python
# 基本用法
result = await client.call_tool("audio_to_text_converter", {
    "audio_file_path": "/path/to/audio/file.mp3"
})

# 带详细参数的用法
result = await client.call_tool("audio_to_text_converter", {
    "audio_file_path": "/path/to/chinese_audio.wav",
    "parameters": {
        "model": "whisper-1",
        "language": "zh",
        "response_format": "json",
        "prompt": "这是一段中文音频",
        "temperature": 0.0
    }
})
```

## 开发指南

### 添加新的转换器

1. 在 `backend/app/core/converters/` 目录中创建新的转换器类
2. 继承 `AbstractConverter` 基类
3. 实现必要的方法和属性
4. 在转换器注册函数中注册新转换器
5. MCP 服务器会自动检测并生成对应的工具

### 自定义 MCP 工具

可以在 `backend/app/core/mcp/tools.py` 中添加自定义的 MCP 工具：

```python
@mcp_server.tool()
async def custom_tool(param1: str, param2: int, ctx: Context = None) -> Dict[str, Any]:
    """自定义工具描述"""
    if ctx:
        await ctx.info("执行自定义工具...")

    # 工具逻辑
    return {"result": "success"}
```

## 故障排除

### 常见问题

1. **MCP 服务器无法启动**
   - 检查端口是否被占用
   - 确认 FastMCP 依赖已正确安装
   - 查看日志输出获取详细错误信息

2. **转换器工具未出现**
   - 确认转换器已正确注册
   - 检查转换器类是否继承了 `AbstractConverter`
   - 启用监控功能以自动检测变化

3. **工具执行失败**
   - 检查输入参数格式是否正确
   - 确认相关服务（ComfyUI、LLM、Audio API）配置正确
   - 查看服务器日志获取详细错误信息

### 日志调试

启用详细日志输出：

```bash
python mcp_server.py --verbose
python test_mcp_client.py --verbose
```

## 快速开始

### 1. 安装依赖

确保已安装所有依赖：

```bash
cd backend
pip install -r requirements.txt
```

### 2. 运行演示

```bash
# 运行交互式演示
python demo_mcp_usage.py

# 运行测试客户端
python test_mcp_client.py --transport memory
```

### 3. 启动独立 MCP 服务器

```bash
# STDIO 模式（用于 Claude Desktop 等客户端）
python mcp_server.py

# Web 模式（用于 HTTP 客户端）
python mcp_server.py --transport sse --port 8001
```

## 成功验证

✅ **MCP 服务器集成完成**：
- 自动为每个转换器生成 MCP 工具
- 支持动态转换器发现和更新
- 提供完整的服务器信息和健康检查
- 支持多种传输方式（STDIO、SSE、Streamable HTTP）

✅ **测试验证通过**：
- 内存模式连接测试成功
- 工具注册和调用正常工作（注册了 3 个转换器工具）
- 服务器信息和状态查询功能正常
- 转换器列表和详情查询功能正常
- **文件路径输出修复**：MCP 工具调用后正确返回输出文件的 `file_path`

✅ **功能完整性验证**：
- 文本到图像转换工具正常工作
- 文本到音频转换工具正常工作并返回安全的 HTTP URL
- 音频到文本转换工具正常注册
- 输出媒体自动保存到文件系统和数据库
- 返回结果包含 HTTP 访问 URL、媒体 ID 和预览信息

✅ **安全性改进**：
- **不再暴露服务器文件路径**：所有响应都使用 HTTP URL 而不是文件路径
- **统一的 URL 生成**：使用 `generate_media_url()` 函数生成标准的媒体访问 URL
- **响应数据清理**：使用 `sanitize_response_for_security()` 自动移除敏感字段
- **API 和 MCP 一致性**：API 接口和 MCP 工具都遵循相同的安全标准

✅ **参数格式改进**：
- **详细的参数定义**：使用 Pydantic 模型定义具体的参数 schema
- **类型安全**：所有参数都有明确的类型、默认值和验证规则
- **完整的文档**：每个工具都有详细的参数说明和使用示例
- **FastMCP 最佳实践**：使用 `add_tool()` 方法而不是装饰器，避免方法绑定问题

✅ **动态配置支持**：
- **可配置的枚举值**：所有 enum 参数（语音类型、模型名称等）都可以在 admin 界面配置
- **参数范围配置**：图像尺寸、速度、温度等参数的范围可以动态调整
- **实时更新**：修改配置后 MCP 工具会自动刷新，无需重启服务
- **Admin 界面集成**：在 admin.html 中提供了专门的 MCP 配置界面

✅ **文档和示例完整**：
- 提供了详细的使用文档
- 包含交互式演示脚本
- 提供了测试客户端
- 支持独立运行模式

## 依赖要求

- FastMCP >= 2.5.1
- FastAPI
- 其他 TransMediaX 依赖

确保在 `backend/requirements.txt` 中包含了 `fastmcp==2.5.1`。
