<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TransMediaX - Admin</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">TransMediaX</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="admin.html">Admin</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1>Admin Configuration</h1>

        <div id="loginForm" class="card mt-4">
            <div class="card-header">
                <h5>Admin Login</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="username">
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" class="form-control" id="password">
                </div>
                <button id="loginBtn" class="btn btn-primary">Login</button>
                <div id="loginError" class="text-danger mt-2 d-none">Invalid credentials</div>
            </div>
        </div>

        <div id="adminPanel" class="d-none">
            <div class="card mt-4">
                <div class="card-header">
                    <h5>API Configuration</h5>
                </div>
                <div class="card-body">
                    <h6 class="mb-3">Speech to Text Service (OpenAI Compatible)</h6>
                    <div class="mb-3">
                        <label for="sttApiKey" class="form-label">STT API Key</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="sttApiKey">
                            <button class="btn btn-outline-secondary" type="button" onclick="document.getElementById('sttApiKey').value = ''">清除</button>
                        </div>
                        <small class="form-text text-muted">如需清除API密钥，请点击清除按钮后保存</small>
                    </div>
                    <div class="mb-3">
                        <label for="sttBaseUrl" class="form-label">STT Base URL</label>
                        <input type="text" class="form-control" id="sttBaseUrl">
                    </div>
                    <div class="mb-3">
                        <label for="sttModel" class="form-label">STT Model</label>
                        <input type="text" class="form-control" id="sttModel">
                    </div>

                    <h6 class="mb-3">Text to Speech Service (OpenAI Compatible)</h6>
                    <div class="mb-3">
                        <label for="ttsApiKey" class="form-label">TTS API Key</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="ttsApiKey">
                            <button class="btn btn-outline-secondary" type="button" onclick="document.getElementById('ttsApiKey').value = ''">清除</button>
                        </div>
                        <small class="form-text text-muted">如需清除API密钥，请点击清除按钮后保存</small>
                    </div>
                    <div class="mb-3">
                        <label for="ttsBaseUrl" class="form-label">TTS Base URL</label>
                        <input type="text" class="form-control" id="ttsBaseUrl">
                    </div>
                    <div class="mb-3">
                        <label for="ttsModel" class="form-label">TTS Model</label>
                        <input type="text" class="form-control" id="ttsModel">
                    </div>

                    <h6 class="mb-3">LLM Service (OpenAI Compatible)</h6>
                    <div class="mb-3">
                        <label for="llmApiKey" class="form-label">LLM API Key</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="llmApiKey">
                            <button class="btn btn-outline-secondary" type="button" onclick="document.getElementById('llmApiKey').value = ''">清除</button>
                        </div>
                        <small class="form-text text-muted">如需清除API密钥，请点击清除按钮后保存</small>
                    </div>
                    <div class="mb-3">
                        <label for="llmBaseUrl" class="form-label">LLM Base URL</label>
                        <input type="text" class="form-control" id="llmBaseUrl">
                    </div>
                    <div class="mb-3">
                        <label for="llmModel" class="form-label">LLM Model</label>
                        <input type="text" class="form-control" id="llmModel">
                    </div>

                    <h6 class="mb-3">Image Generation Service</h6>
                    <div class="mb-3">
                        <label for="comfyuiBaseUrl" class="form-label">ComfyUI Base URL</label>
                        <input type="text" class="form-control" id="comfyuiBaseUrl">
                    </div>
                    <button id="saveConfigBtn" class="btn btn-primary">Save Configuration</button>
                    <div id="configSaveSuccess" class="text-success mt-2 d-none">Configuration saved successfully</div>
                    <div id="configSaveError" class="text-danger mt-2 d-none">Error saving configuration</div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5>Converter Configuration</h5>
                    <small class="text-muted">Global settings for all converters (API and MCP)</small>
                </div>
                <div class="card-body">
                    <h6 class="mb-3">Text to Speech (TTS) Settings</h6>
                    <div class="mb-3">
                        <label for="ttsAvailableVoices" class="form-label">Available Voices</label>
                        <input type="text" class="form-control" id="ttsAvailableVoices">
                        <small class="form-text text-muted">用逗号分隔多个语音类型</small>
                    </div>
                    <div class="mb-3">
                        <label for="ttsAvailableModels" class="form-label">Available Models</label>
                        <input type="text" class="form-control" id="ttsAvailableModels">
                        <small class="form-text text-muted">用逗号分隔多个模型</small>
                    </div>
                    <div class="mb-3">
                        <label for="ttsAvailableFormats" class="form-label">Available Formats</label>
                        <input type="text" class="form-control" id="ttsAvailableFormats">
                        <small class="form-text text-muted">用逗号分隔多个音频格式</small>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="ttsDefaultVoice" class="form-label">Default Voice</label>
                                <input type="text" class="form-control" id="ttsDefaultVoice">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="ttsDefaultModel" class="form-label">Default Model</label>
                                <input type="text" class="form-control" id="ttsDefaultModel">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="ttsDefaultFormat" class="form-label">Default Format</label>
                                <input type="text" class="form-control" id="ttsDefaultFormat">
                            </div>
                        </div>
                    </div>

                    <h6 class="mb-3">Speech to Text (STT) Settings</h6>
                    <div class="mb-3">
                        <label for="sttAvailableModels" class="form-label">Available Models</label>
                        <input type="text" class="form-control" id="sttAvailableModels">
                        <small class="form-text text-muted">用逗号分隔多个模型</small>
                    </div>
                    <div class="mb-3">
                        <label for="sttAvailableResponseFormats" class="form-label">Response Formats</label>
                        <input type="text" class="form-control" id="sttAvailableResponseFormats">
                        <small class="form-text text-muted">用逗号分隔多个响应格式</small>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sttDefaultModel" class="form-label">Default Model</label>
                                <input type="text" class="form-control" id="sttDefaultModel">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sttDefaultResponseFormat" class="form-label">Default Response Format</label>
                                <input type="text" class="form-control" id="sttDefaultResponseFormat">
                            </div>
                        </div>
                    </div>

                    <h6 class="mb-3">Image Generation Settings</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="imageSizeMin" class="form-label">Min Image Size</label>
                                <input type="number" class="form-control" id="imageSizeMin" min="64" max="2048">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="imageSizeMax" class="form-label">Max Image Size</label>
                                <input type="number" class="form-control" id="imageSizeMax" min="64" max="2048">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="imageSizeDefault" class="form-label">Default Size</label>
                                <input type="number" class="form-control" id="imageSizeDefault" min="64" max="2048">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="imageCountMax" class="form-label">Max Image Count</label>
                                <input type="number" class="form-control" id="imageCountMax" min="1" max="10">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="imageCountDefault" class="form-label">Default Image Count</label>
                                <input type="number" class="form-control" id="imageCountDefault" min="1" max="10">
                            </div>
                        </div>
                    </div>

                    <h6 class="mb-3">Parameter Ranges</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="ttsSpeedMin" class="form-label">TTS Speed Min</label>
                                <input type="number" class="form-control" id="ttsSpeedMin" step="0.1" min="0.1" max="4.0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="ttsSpeedMax" class="form-label">TTS Speed Max</label>
                                <input type="number" class="form-control" id="ttsSpeedMax" step="0.1" min="0.1" max="4.0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="ttsSpeedDefault" class="form-label">TTS Speed Default</label>
                                <input type="number" class="form-control" id="ttsSpeedDefault" step="0.1" min="0.1" max="4.0">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="imageCfgScaleMin" class="form-label">CFG Scale Min</label>
                                <input type="number" class="form-control" id="imageCfgScaleMin" step="0.1" min="1.0" max="20.0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="imageCfgScaleMax" class="form-label">CFG Scale Max</label>
                                <input type="number" class="form-control" id="imageCfgScaleMax" step="0.1" min="1.0" max="20.0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="imageCfgScaleDefault" class="form-label">CFG Scale Default</label>
                                <input type="number" class="form-control" id="imageCfgScaleDefault" step="0.1" min="1.0" max="20.0">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="imageStepsMin" class="form-label">Steps Min</label>
                                <input type="number" class="form-control" id="imageStepsMin" min="1" max="100">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="imageStepsMax" class="form-label">Steps Max</label>
                                <input type="number" class="form-control" id="imageStepsMax" min="1" max="100">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="imageStepsDefault" class="form-label">Steps Default</label>
                                <input type="number" class="form-control" id="imageStepsDefault" min="1" max="100">
                            </div>
                        </div>
                    </div>

                    <button id="saveConverterConfigBtn" class="btn btn-primary">Save Converter Configuration</button>
                    <div id="converterConfigSaveSuccess" class="text-success mt-2 d-none">Converter configuration saved successfully</div>
                    <div id="converterConfigSaveError" class="text-danger mt-2 d-none">Error saving converter configuration</div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5>Available Converters</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Input Types</th>
                                <th>Output Types</th>
                            </tr>
                        </thead>
                        <tbody id="convertersTable">
                            <!-- Converters will be added here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5>System Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Database Path</label>
                        <input type="text" class="form-control" id="databasePath" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Media Storage Path</label>
                        <input type="text" class="form-control" id="mediaStoragePath" readonly>
                    </div>
                    <button id="restartBtn" class="btn btn-danger">Restart Server</button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/api.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>
