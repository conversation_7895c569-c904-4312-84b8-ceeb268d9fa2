from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Request
from sqlmodel import Session, select, delete
import uuid
import logging

from ....database import get_session
from ....models.task import Task
from ....models.media import Media, TaskInputMedia, TaskOutputMedia
from ....schemas.task import TaskCreate, Task as TaskSchema, TaskWithProgress
from ....core.converters.base import ConverterRegistry, ConversionTask
from ....core.media.base import AbstractMedia
from ....core.media.text import TextMedia
from ....core.media.image import ImageMedia
from ....core.media.audio import AudioMedia
from ....core.media.video import VideoMedia
from ....core.media.model3d import Model3DMedia
from ....core.utils.url_helper import get_base_url_from_request, sanitize_response_for_security

logger = logging.getLogger(__name__)

router = APIRouter()


def get_media_class(media_type: str) -> type:
    """Get the media class for a media type."""
    media_classes = {
        "text": TextMedia,
        "image": ImageMedia,
        "audio": AudioMedia,
        "video": VideoMedia,
        "model3d": Model3DMedia
    }
    return media_classes.get(media_type)


def load_media_from_db(db_media: Media) -> Optional[AbstractMedia]:
    """Load a media object from the database."""
    media_class = get_media_class(db_media.media_type)
    if not media_class:
        return None

    media = media_class()
    if db_media.file_path:
        try:
            media.load(db_media.file_path)
            return media
        except Exception as e:
            logger.error(f"Error loading media {db_media.id}: {e}")
            return None
    return None


def process_task(task_id: str, session: Session):
    """Process a conversion task in the background."""
    # Get the task from the database
    task = session.get(Task, task_id)
    if not task:
        logger.error(f"Task {task_id} not found")
        return

    # Update task status
    task.status = "processing"
    task.progress = 0.0
    session.add(task)
    session.commit()

    try:
        # Get input media
        input_media_db = session.exec(
            select(Media).join(TaskInputMedia).where(TaskInputMedia.task_id == task_id)
        ).all()

        # Load input media objects
        input_media = []
        for db_media in input_media_db:
            media = load_media_from_db(db_media)
            if media:
                input_media.append(media)

        if not input_media:
            task.status = "failed"
            task.error_message = "No valid input media found"
            session.add(task)
            session.commit()
            return

        # Create conversion task
        conversion_task = ConversionTask(
            id=task_id,
            user_id=task.user_id,
            status="processing",
            parameters=task.parameters,
            input_media_ids=[m.get_metadata().id for m in input_media]
        )

        # Find appropriate converter
        input_types = [type(m) for m in input_media]
        output_type_str = task.task_type.split("_to_")[1]
        output_type = get_media_class(output_type_str)

        if not output_type:
            task.status = "failed"
            task.error_message = f"Invalid output type: {output_type_str}"
            session.add(task)
            session.commit()
            return

        # Find converter
        converter = ConverterRegistry.find_converter(input_types, output_type)

        if not converter:
            # Try to find a conversion path
            conversion_path = ConverterRegistry.find_conversion_path(input_types, output_type)
            if not conversion_path:
                task.status = "failed"
                task.error_message = f"No converter found for {input_types} to {output_type}"
                session.add(task)
                session.commit()
                return

            # Execute conversion path
            current_media = input_media
            for path_converter in conversion_path:
                # Create a subtask for this conversion step
                subtask = ConversionTask(
                    id=str(uuid.uuid4()),
                    user_id=task.user_id,
                    status="processing",
                    parameters=task.parameters,
                    input_media_ids=[m.get_metadata().id for m in current_media]
                )

                # Convert
                current_media = path_converter.convert(subtask, current_media)

                if not current_media:
                    task.status = "failed"
                    task.error_message = f"Conversion step failed: {subtask.error_message}"
                    session.add(task)
                    session.commit()
                    return

            # Final output media
            output_media = current_media
        else:
            # Direct conversion
            output_media = converter.convert(conversion_task, input_media)

        if not output_media:
            task.status = "failed"
            task.error_message = conversion_task.error_message or "Conversion failed"
            session.add(task)
            session.commit()
            return

        # Save output media to database
        for media in output_media:
            # Save media file
            from ....config import settings
            media_path = media.save(settings.MEDIA_STORAGE_PATH)

            # Create media record
            metadata = media.get_metadata()
            db_media = Media(
                id=metadata.id,
                user_id=task.user_id,
                media_type=output_type_str,
                file_path=media_path,
                content_type=metadata.content_type,
                metadatas=metadata.metadatas
            )
            session.add(db_media)

            # Link media to task
            task_output_media = TaskOutputMedia(task_id=task_id, media_id=db_media.id)
            session.add(task_output_media)

        # Update task status
        task.status = "completed"
        task.progress = 1.0
        session.add(task)
        session.commit()

    except Exception as e:
        logger.exception(f"Error processing task {task_id}: {e}")
        task.status = "failed"
        task.error_message = str(e)
        session.add(task)
        session.commit()


@router.post("/", response_model=TaskSchema)
def create_task(
    task_in: TaskCreate,
    background_tasks: BackgroundTasks,
    session: Session = Depends(get_session)
):
    """Create a new conversion task."""
    # Validate input media
    input_media = []
    for media_id in task_in.input_media_ids:
        db_media = session.get(Media, media_id)
        if not db_media:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Media {media_id} not found"
            )
        input_media.append(db_media)

    # Create task
    task = Task(
        user_id=task_in.user_id,
        status="pending",
        task_type=task_in.task_type,
        parameters=task_in.parameters
    )
    session.add(task)
    session.commit()
    session.refresh(task)

    # Link input media to task
    for media in input_media:
        task_input_media = TaskInputMedia(task_id=task.id, media_id=media.id)
        session.add(task_input_media)

    session.commit()

    # Start processing task in background
    background_tasks.add_task(process_task, task.id, session)

    return task


@router.get("/{task_id}/", response_model=TaskSchema)
def get_task(task_id: str, session: Session = Depends(get_session)):
    """Get a task by ID."""
    task = session.get(Task, task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found"
        )

    # Get input and output media
    input_media = session.exec(
        select(Media).join(TaskInputMedia).where(TaskInputMedia.task_id == task_id)
    ).all()

    output_media = session.exec(
        select(Media).join(TaskOutputMedia).where(TaskOutputMedia.task_id == task_id)
    ).all()

    # Create response by converting all objects to dictionaries first
    # Try to use model_dump() (Pydantic v2) or fall back to dict() (Pydantic v1)
    try:
        task_dict = task.model_dump()
    except AttributeError:
        task_dict = task.dict()

    # Convert input and output media to dictionaries
    input_media_dicts = []
    for media in input_media:
        try:
            input_media_dicts.append(media.model_dump())
        except AttributeError:
            input_media_dicts.append(media.dict())

    output_media_dicts = []
    for media in output_media:
        try:
            output_media_dicts.append(media.model_dump())
        except AttributeError:
            output_media_dicts.append(media.dict())

    task_dict["input_media"] = input_media_dicts
    task_dict["output_media"] = output_media_dicts

    # Use model_validate (Pydantic v2) or parse_obj (Pydantic v1)
    try:
        response = TaskSchema.model_validate(task_dict)
    except AttributeError:
        response = TaskSchema.parse_obj(task_dict)

    return response


@router.get("/{task_id}/progress/", response_model=TaskWithProgress)
def get_task_progress(task_id: str, session: Session = Depends(get_session)):
    """Get the progress of a task."""
    task = session.get(Task, task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found"
        )

    # If task is still processing, try to get more accurate progress from converter
    if task.status == "processing":
        # Find the appropriate converter
        input_types = []
        input_media_db = session.exec(
            select(Media).join(TaskInputMedia).where(TaskInputMedia.task_id == task_id)
        ).all()

        for db_media in input_media_db:
            media_class = get_media_class(db_media.media_type)
            if media_class:
                input_types.append(media_class)

        output_type_str = task.task_type.split("_to_")[1]
        output_type = get_media_class(output_type_str)

        if input_types and output_type:
            converter = ConverterRegistry.find_converter(input_types, output_type)
            if converter:
                progress_info = converter.get_progress(task_id)
                task.progress = progress_info.get("progress", task.progress)
                task.current_step = progress_info.get("current_step", task.current_step)
                session.add(task)
                session.commit()

    return TaskWithProgress(
        id=task.id,
        status=task.status,
        progress=task.progress,
        current_step=task.current_step,
        error_message=task.error_message
    )


@router.get("/", response_model=List[TaskSchema])
def list_tasks(
    user_id: Optional[str] = None,
    status: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    session: Session = Depends(get_session)
):
    """List tasks with optional filtering."""
    query = select(Task)

    if user_id:
        query = query.where(Task.user_id == user_id)

    if status:
        query = query.where(Task.status == status)

    query = query.order_by(Task.created_at.desc()).offset(offset).limit(limit)
    tasks = session.exec(query).all()

    # Get input and output media for each task
    result = []
    for task in tasks:
        input_media = session.exec(
            select(Media).join(TaskInputMedia).where(TaskInputMedia.task_id == task.id)
        ).all()

        output_media = session.exec(
            select(Media).join(TaskOutputMedia).where(TaskOutputMedia.task_id == task.id)
        ).all()

        # Create response by converting all objects to dictionaries first
        # Try to use model_dump() (Pydantic v2) or fall back to dict() (Pydantic v1)
        try:
            task_dict = task.model_dump()
        except AttributeError:
            task_dict = task.dict()

        # Convert input and output media to dictionaries
        input_media_dicts = []
        for media in input_media:
            try:
                input_media_dicts.append(media.model_dump())
            except AttributeError:
                input_media_dicts.append(media.dict())

        output_media_dicts = []
        for media in output_media:
            try:
                output_media_dicts.append(media.model_dump())
            except AttributeError:
                output_media_dicts.append(media.dict())

        task_dict["input_media"] = input_media_dicts
        task_dict["output_media"] = output_media_dicts

        # Use model_validate (Pydantic v2) or parse_obj (Pydantic v1)
        try:
            task_schema = TaskSchema.model_validate(task_dict)
        except AttributeError:
            task_schema = TaskSchema.parse_obj(task_dict)

        result.append(task_schema)

    return result


@router.delete("/{task_id}/", status_code=status.HTTP_204_NO_CONTENT)
def delete_task(task_id: str, session: Session = Depends(get_session)):
    """Delete a task."""
    task = session.get(Task, task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found"
        )

    # Delete task input and output media links
    session.exec(delete(TaskInputMedia).where(TaskInputMedia.task_id == task_id))
    session.exec(delete(TaskOutputMedia).where(TaskOutputMedia.task_id == task_id))

    # Delete task
    session.delete(task)
    session.commit()

    return None


@router.delete("/", status_code=status.HTTP_204_NO_CONTENT)
def delete_all_tasks(
    user_id: Optional[str] = None,
    session: Session = Depends(get_session)
):
    """Delete all tasks, optionally filtered by user_id."""
    query = select(Task)

    if user_id:
        query = query.where(Task.user_id == user_id)

    tasks = session.exec(query).all()

    if not tasks:
        return None

    # Delete all task input and output media links for these tasks
    task_ids = [task.id for task in tasks]
    session.exec(delete(TaskInputMedia).where(TaskInputMedia.task_id.in_(task_ids)))
    session.exec(delete(TaskOutputMedia).where(TaskOutputMedia.task_id.in_(task_ids)))

    # Delete all tasks
    for task in tasks:
        session.delete(task)

    session.commit()

    return None
