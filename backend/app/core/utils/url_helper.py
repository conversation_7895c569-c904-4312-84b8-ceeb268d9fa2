"""URL 辅助工具

提供统一的 URL 生成功能，避免暴露服务器文件路径。
"""

from typing import Optional
from ..media.base import AbstractMedia
from ...config import settings


def generate_media_url(media_id: str, base_url: Optional[str] = None) -> str:
    """
    生成媒体文件的 HTTP 访问 URL
    
    Args:
        media_id: 媒体文件 ID
        base_url: 基础 URL，如果不提供则使用默认值
        
    Returns:
        媒体文件的 HTTP 访问 URL
    """
    if base_url is None:
        # 默认使用本地 API 地址
        base_url = f"http://localhost:8000{settings.API_V1_STR}"
    
    return f"{base_url}/media/{media_id}/file"


def generate_media_preview_url(media_id: str, base_url: Optional[str] = None) -> str:
    """
    生成媒体预览的 HTTP 访问 URL
    
    Args:
        media_id: 媒体文件 ID
        base_url: 基础 URL，如果不提供则使用默认值
        
    Returns:
        媒体预览的 HTTP 访问 URL
    """
    if base_url is None:
        # 默认使用本地 API 地址
        base_url = f"http://localhost:8000{settings.API_V1_STR}"
    
    return f"{base_url}/media/{media_id}/preview"


def convert_media_to_url_response(media: AbstractMedia, base_url: Optional[str] = None) -> dict:
    """
    将媒体对象转换为包含 URL 的响应格式
    
    Args:
        media: 媒体对象
        base_url: 基础 URL，如果不提供则使用默认值
        
    Returns:
        包含 URL 而非文件路径的响应字典
    """
    metadata = media.get_metadata()
    media_id = metadata.id
    
    # 生成 URL
    file_url = generate_media_url(media_id, base_url)
    preview_url = generate_media_preview_url(media_id, base_url)
    
    # 获取预览数据
    preview_data = media.get_preview() if hasattr(media, 'get_preview') else None
    
    # 如果预览数据是 data URL，直接使用；否则使用预览 URL
    if preview_data and preview_data.startswith("data:"):
        final_preview = preview_data
    else:
        final_preview = preview_url
    
    response = {
        "id": media_id,
        "type": type(media).__name__.replace("Media", "").lower(),
        "file_url": file_url,
        "preview_url": final_preview,
        "metadata": metadata.metadatas,
        "content_type": metadata.content_type,
        "created_at": metadata.created_at.isoformat() if metadata.created_at else None,
        "updated_at": metadata.updated_at.isoformat() if metadata.updated_at else None
    }
    
    # 对于文本媒体，还包含内容
    if hasattr(media, 'content'):
        response["content"] = media.content
    
    return response


def get_base_url_from_request(request) -> str:
    """
    从 FastAPI 请求对象中获取基础 URL
    
    Args:
        request: FastAPI Request 对象
        
    Returns:
        基础 URL
    """
    scheme = request.url.scheme
    host = request.headers.get("host", request.url.netloc)
    return f"{scheme}://{host}{settings.API_V1_STR}"


def sanitize_response_for_security(response_data: dict) -> dict:
    """
    清理响应数据，移除可能暴露服务器信息的字段
    
    Args:
        response_data: 原始响应数据
        
    Returns:
        清理后的响应数据
    """
    # 需要移除的敏感字段
    sensitive_fields = [
        "file_path",
        "absolute_path", 
        "local_path",
        "server_path",
        "storage_path"
    ]
    
    # 递归清理字典
    def clean_dict(data):
        if isinstance(data, dict):
            cleaned = {}
            for key, value in data.items():
                if key not in sensitive_fields:
                    cleaned[key] = clean_dict(value)
            return cleaned
        elif isinstance(data, list):
            return [clean_dict(item) for item in data]
        else:
            return data
    
    return clean_dict(response_data)
