import os
import threading
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import logging

from .config import settings
from .database import create_db_and_tables
from .api.v1.router import api_router
from .core.services.manager import service_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
)

# Set up CORS
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

# Service clients are managed by the ServiceManager

# MCP Server instance
mcp_server = None
mcp_thread = None

# Mount static files for frontend
frontend_dir = os.path.join(os.getcwd(), "frontend")
if os.path.exists(frontend_dir):
    app.mount("/", StaticFiles(directory=frontend_dir, html=True), name="frontend")


def start_mcp_server():
    """在单独线程中启动 MCP 服务器"""
    global mcp_server

    if not settings.MCP_ENABLED:
        logger.info("MCP 服务器已禁用")
        return

    try:
        from .core.mcp.server import initialize_mcp_server

        # 初始化 MCP 服务器
        mcp_server = initialize_mcp_server(settings.MCP_SERVER_NAME)

        # 启动监控（如果启用）
        if settings.MCP_MONITORING_ENABLED:
            mcp_server.start_monitoring(settings.MCP_MONITORING_INTERVAL)

        # 根据配置的传输方式启动服务器
        if settings.MCP_TRANSPORT == "stdio":
            mcp_server.run_stdio()
        elif settings.MCP_TRANSPORT == "sse":
            mcp_server.run_sse(settings.MCP_HOST, settings.MCP_PORT, settings.MCP_PATH)
        elif settings.MCP_TRANSPORT == "streamable-http":
            mcp_server.run_streamable_http(settings.MCP_HOST, settings.MCP_PORT, settings.MCP_PATH)
        else:
            logger.error(f"不支持的 MCP 传输方式: {settings.MCP_TRANSPORT}")

    except Exception as e:
        logger.exception(f"启动 MCP 服务器失败: {e}")


@app.on_event("startup")
def on_startup():
    """Startup event handler."""
    global mcp_thread

    # Create database and tables
    create_db_and_tables()

    # Create necessary directories
    os.makedirs(settings.MEDIA_STORAGE_PATH, exist_ok=True)

    # 注册转换器（使用动态配置）
    service_manager.register_converters()

    # 启动 MCP 服务器（在单独线程中）
    if settings.MCP_ENABLED and settings.MCP_TRANSPORT != "stdio":
        mcp_thread = threading.Thread(target=start_mcp_server, daemon=True)
        mcp_thread.start()
        logger.info(f"MCP 服务器线程已启动，传输方式: {settings.MCP_TRANSPORT}")
    elif settings.MCP_ENABLED:
        logger.info("MCP 服务器配置为 STDIO 模式，请单独运行")


@app.on_event("shutdown")
def on_shutdown():
    """Shutdown event handler."""
    global mcp_server

    # 停止 MCP 服务器监控
    if mcp_server:
        try:
            mcp_server.stop_monitoring()
            logger.info("MCP 服务器已停止")
        except Exception as e:
            logger.exception(f"停止 MCP 服务器时发生错误: {e}")


@app.get("/")
def read_root():
    """Root endpoint."""
    return {"message": "Welcome to TransMediaX API"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
