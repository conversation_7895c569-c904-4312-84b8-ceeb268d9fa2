#!/usr/bin/env python3
"""
独立的 MCP 服务器启动脚本

该脚本可以独立运行 TransMediaX 的 MCP 服务器，支持不同的传输方式。

使用方法:
    # STDIO 模式（默认）
    python mcp_server.py
    
    # SSE 模式
    python mcp_server.py --transport sse --host 127.0.0.1 --port 8001
    
    # Streamable HTTP 模式
    python mcp_server.py --transport streamable-http --host 127.0.0.1 --port 8001
"""

import argparse
import logging
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.config import settings
from app.core.services.manager import service_manager
from app.core.mcp.server import initialize_mcp_server
from app.database import create_db_and_tables

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def setup_environment():
    """设置环境"""
    # 创建数据库和表
    create_db_and_tables()
    
    # 创建必要的目录
    os.makedirs(settings.MEDIA_STORAGE_PATH, exist_ok=True)
    
    # 注册转换器
    service_manager.register_converters()
    
    logger.info("环境设置完成")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="TransMediaX MCP 服务器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        "--transport",
        choices=["stdio", "sse", "streamable-http"],
        default="stdio",
        help="传输方式 (默认: stdio)"
    )
    
    parser.add_argument(
        "--host",
        default="127.0.0.1",
        help="服务器主机地址 (默认: 127.0.0.1)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8001,
        help="服务器端口 (默认: 8001)"
    )
    
    parser.add_argument(
        "--path",
        default="/mcp",
        help="MCP 路径 (默认: /mcp)"
    )
    
    parser.add_argument(
        "--name",
        default="TransMediaX MCP Server",
        help="MCP 服务器名称"
    )
    
    parser.add_argument(
        "--monitoring",
        action="store_true",
        default=True,
        help="启用转换器监控 (默认: 启用)"
    )
    
    parser.add_argument(
        "--monitoring-interval",
        type=int,
        default=30,
        help="监控检查间隔（秒） (默认: 30)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细日志输出"
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info(f"启动 TransMediaX MCP 服务器")
    logger.info(f"传输方式: {args.transport}")
    
    if args.transport != "stdio":
        logger.info(f"地址: {args.host}:{args.port}{args.path}")
    
    try:
        # 设置环境
        setup_environment()
        
        # 初始化 MCP 服务器
        mcp_server = initialize_mcp_server(args.name)
        
        # 启动监控（如果启用）
        if args.monitoring:
            mcp_server.start_monitoring(args.monitoring_interval)
            logger.info(f"转换器监控已启用，检查间隔: {args.monitoring_interval}秒")
        
        # 根据传输方式启动服务器
        if args.transport == "stdio":
            logger.info("以 STDIO 模式启动 MCP 服务器")
            mcp_server.run_stdio()
        elif args.transport == "sse":
            logger.info(f"以 SSE 模式启动 MCP 服务器: {args.host}:{args.port}{args.path}")
            mcp_server.run_sse(args.host, args.port, args.path)
        elif args.transport == "streamable-http":
            logger.info(f"以 Streamable HTTP 模式启动 MCP 服务器: {args.host}:{args.port}{args.path}")
            mcp_server.run_streamable_http(args.host, args.port, args.path)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止服务器...")
    except Exception as e:
        logger.exception(f"启动 MCP 服务器失败: {e}")
        sys.exit(1)
    finally:
        logger.info("MCP 服务器已停止")


if __name__ == "__main__":
    main()
