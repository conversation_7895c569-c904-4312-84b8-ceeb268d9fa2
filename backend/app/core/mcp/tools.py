"""MCP 工具生成器

该模块负责根据转换器自动生成 MCP 工具。
"""

import logging
from typing import Dict, Any, List, Optional, Type, Union
from fastmcp import FastMCP, Context
from pydantic import BaseModel, Field
import uuid
import asyncio

from ..converters.base import AbstractConverter, ConversionTask, ConverterRegistry
from ..media.base import AbstractMedia
from ..media.text import TextMedia
from ..media.image import ImageMedia
from ..media.audio import AudioMedia
from ..utils.url_helper import convert_media_to_url_response, sanitize_response_for_security

logger = logging.getLogger(__name__)


# 定义详细的参数模型
class TextToImageParameters(BaseModel):
    """文本到图像转换参数"""
    width: int = Field(default=512, description="图像宽度（像素）", ge=64, le=2048)
    height: int = Field(default=512, description="图像高度（像素）", ge=64, le=2048)
    num_images: int = Field(default=1, description="生成图像数量", ge=1, le=4)
    positive_prompt: Optional[str] = Field(default=None, description="正向提示词（可选，系统会自动生成）")
    negative_prompt: Optional[str] = Field(default=None, description="负向提示词（可选，系统会自动生成）")
    steps: Optional[int] = Field(default=None, description="推理步数", ge=1, le=100)
    cfg_scale: Optional[float] = Field(default=None, description="CFG 引导强度", ge=1.0, le=20.0)
    seed: Optional[int] = Field(default=None, description="随机种子（可选）")


class TextToAudioParameters(BaseModel):
    """文本到音频转换参数"""
    voice: str = Field(default="alloy", description="语音类型",
                      enum=["alloy", "echo", "fable", "onyx", "nova", "shimmer"])
    speed: float = Field(default=1.0, description="语音速度", ge=0.25, le=4.0)
    model: str = Field(default="tts-1", description="TTS 模型", enum=["tts-1", "tts-1-hd"])
    format: str = Field(default="mp3", description="音频格式", enum=["mp3", "opus", "aac", "flac"])


class AudioToTextParameters(BaseModel):
    """音频到文本转换参数"""
    model: str = Field(default="whisper-1", description="ASR 模型", enum=["whisper-1"])
    language: Optional[str] = Field(default=None, description="音频语言（ISO 639-1 代码，如 'zh', 'en'）")
    prompt: Optional[str] = Field(default=None, description="提示文本，用于改善识别准确性")
    response_format: str = Field(default="text", description="响应格式",
                                enum=["json", "text", "srt", "verbose_json", "vtt"])
    temperature: float = Field(default=0.0, description="采样温度", ge=0.0, le=1.0)


class ConversionResult(BaseModel):
    """转换结果模型"""
    success: bool = Field(description="转换是否成功")
    task_id: str = Field(description="任务 ID")
    result: Dict[str, Any] = Field(description="转换结果详情")
    output_type: str = Field(description="输出媒体类型")
    media_ids: List[str] = Field(description="生成的媒体 ID 列表")
    error: Optional[str] = Field(default=None, description="错误信息（如果失败）")


class ConverterToolGenerator:
    """转换器工具生成器，为每个转换器自动生成 MCP 工具"""

    def __init__(self, mcp_server: FastMCP):
        self.mcp_server = mcp_server
        self.registered_converters: Dict[str, AbstractConverter] = {}

    def register_converter_tools(self):
        """注册所有转换器的 MCP 工具"""
        # 获取所有已注册的转换器
        converters = ConverterRegistry.get_all()

        # 清除之前注册的工具（如果有的话）
        self._clear_previous_tools()

        # 为每个转换器注册工具
        for converter in converters:
            self._register_converter_tool(converter)

        logger.info(f"已注册 {len(converters)} 个转换器的 MCP 工具")

    def _clear_previous_tools(self):
        """清除之前注册的转换器工具"""
        # 注意：FastMCP 可能没有直接的清除工具方法，这里我们记录已注册的转换器
        self.registered_converters.clear()

    def _get_converter_type(self, converter: AbstractConverter) -> str:
        """获取转换器类型"""
        input_type = converter.input_types[0].__name__.replace("Media", "").lower()
        output_type = converter.output_types[0].__name__.replace("Media", "").lower()
        return f"{input_type}_to_{output_type}"

    def _register_converter_tool(self, converter: AbstractConverter):
        """为单个转换器注册 MCP 工具"""
        converter_type = self._get_converter_type(converter)
        tool_name = f"convert_{converter_type}"

        # 根据转换器类型创建具体的工具函数
        if converter_type == "text_to_image":
            self._register_text_to_image_tool(converter, tool_name)
        elif converter_type == "text_to_audio":
            self._register_text_to_audio_tool(converter, tool_name)
        elif converter_type == "audio_to_text":
            self._register_audio_to_text_tool(converter, tool_name)
        else:
            # 对于其他类型，使用通用注册方法
            self._register_generic_tool(converter, tool_name)

        # 记录已注册的转换器
        self.registered_converters[tool_name] = converter
        logger.info(f"已注册 MCP 工具: {tool_name}")

    def _register_text_to_image_tool(self, converter: AbstractConverter, tool_name: str):
        """注册文本到图像转换工具"""
        async def text_to_image_converter(
            text: str = Field(description="要转换为图像的文本描述"),
            parameters: Optional[TextToImageParameters] = Field(default=None, description="转换参数"),
            ctx: Context = None
        ) -> ConversionResult:
            """
            将文本转换为图像

            使用 AI 模型将文本描述转换为高质量图像。系统会自动使用大语言模型
            优化您的文本描述，生成更好的正向和负向提示词。

            Args:
                text: 要转换为图像的文本描述，例如："一只可爱的小猫坐在花园里"
                parameters: 可选的转换参数，包括图像尺寸、生成数量等
                ctx: MCP 上下文（自动提供）

            Returns:
                转换结果，包含生成的图像 URL 和相关信息

            Examples:
                # 基本用法
                result = await text_to_image_converter("一只可爱的小猫")

                # 带参数的用法
                params = TextToImageParameters(width=1024, height=768, num_images=2)
                result = await text_to_image_converter("美丽的风景", params)
            """
            return await self._execute_conversion(
                converter, text, parameters.model_dump() if parameters else {}, ctx
            )

        # 使用 add_tool 方法注册
        self.mcp_server.add_tool(text_to_image_converter)

    def _register_text_to_audio_tool(self, converter: AbstractConverter, tool_name: str):
        """注册文本到音频转换工具"""
        async def text_to_audio_converter(
            text: str = Field(description="要转换为音频的文本内容"),
            parameters: Optional[TextToAudioParameters] = Field(default=None, description="转换参数"),
            ctx: Context = None
        ) -> ConversionResult:
            """
            将文本转换为音频

            使用高质量的文本到语音（TTS）技术将文本转换为自然的语音音频。
            支持多种语音类型和音频格式。

            Args:
                text: 要转换为音频的文本内容，例如："你好，欢迎使用 TransMediaX"
                parameters: 可选的转换参数，包括语音类型、速度、格式等
                ctx: MCP 上下文（自动提供）

            Returns:
                转换结果，包含生成的音频文件 URL 和相关信息

            Examples:
                # 基本用法
                result = await text_to_audio_converter("你好世界")

                # 带参数的用法
                params = TextToAudioParameters(voice="nova", speed=1.2, format="mp3")
                result = await text_to_audio_converter("这是一段测试音频", params)
            """
            return await self._execute_conversion(
                converter, text, parameters.model_dump() if parameters else {}, ctx
            )

        # 使用 add_tool 方法注册
        self.mcp_server.add_tool(text_to_audio_converter)

    def _register_audio_to_text_tool(self, converter: AbstractConverter, tool_name: str):
        """注册音频到文本转换工具"""
        async def audio_to_text_converter(
            audio_file_path: str = Field(description="音频文件路径或 URL"),
            parameters: Optional[AudioToTextParameters] = Field(default=None, description="转换参数"),
            ctx: Context = None
        ) -> ConversionResult:
            """
            将音频转换为文本

            使用先进的自动语音识别（ASR）技术将音频文件转换为文本。
            支持多种音频格式和语言。

            Args:
                audio_file_path: 音频文件的路径或 URL
                parameters: 可选的转换参数，包括语言、模型、响应格式等
                ctx: MCP 上下文（自动提供）

            Returns:
                转换结果，包含识别出的文本内容和相关信息

            Examples:
                # 基本用法
                result = await audio_to_text_converter("/path/to/audio.mp3")

                # 带参数的用法
                params = AudioToTextParameters(language="zh", response_format="json")
                result = await audio_to_text_converter("/path/to/audio.wav", params)
            """
            return await self._execute_conversion(
                converter, audio_file_path, parameters.model_dump() if parameters else {}, ctx
            )

        # 使用 add_tool 方法注册
        self.mcp_server.add_tool(audio_to_text_converter)

    def _register_generic_tool(self, converter: AbstractConverter, tool_name: str):
        """注册通用转换工具"""
        async def generic_converter(
            input_content: str = Field(description="输入内容"),
            parameters: Optional[Dict[str, Any]] = Field(default=None, description="转换参数"),
            ctx: Context = None
        ) -> ConversionResult:
            f"""
            {converter.description}

            输入类型: {', '.join([t.__name__ for t in converter.input_types])}
            输出类型: {', '.join([t.__name__ for t in converter.output_types])}

            Args:
                input_content: 输入内容
                parameters: 可选的转换参数
                ctx: MCP 上下文（自动提供）

            Returns:
                转换结果
            """
            return await self._execute_conversion(
                converter, input_content, parameters or {}, ctx
            )

        # 使用 add_tool 方法注册
        self.mcp_server.add_tool(generic_converter)

    async def _execute_conversion(
        self,
        converter: AbstractConverter,
        input_content: str,
        parameters: Dict[str, Any],
        ctx: Context = None
    ) -> ConversionResult:
        """执行转换的核心方法"""
        if ctx:
            await ctx.info(f"开始执行 {converter.name} 转换")

        try:
            # 创建转换任务
            task = ConversionTask(
                parameters=parameters,
                user_id="mcp_user"
            )

            # 根据转换器的输入类型创建输入媒体
            input_media = await self._create_input_media(
                converter.input_types[0],
                input_content
            )

            if ctx:
                await ctx.info(f"正在执行转换...")

            # 执行转换
            output_media = converter.convert(task, [input_media])

            if not output_media:
                raise ValueError("转换失败，没有生成输出")

            # 保存输出媒体到文件系统和数据库
            saved_media_ids = []
            for media in output_media:
                media_id = await self._save_output_media(media, task.user_id, ctx)
                saved_media_ids.append(media_id)

            # 处理输出结果（使用第一个媒体对象生成 URL 响应）
            result = convert_media_to_url_response(output_media[0])

            # 清理响应，确保不暴露敏感信息
            result = sanitize_response_for_security(result)

            if ctx:
                await ctx.info(f"转换完成")

            return ConversionResult(
                success=True,
                task_id=task.id,
                result=result,
                output_type=type(output_media[0]).__name__,
                media_ids=saved_media_ids
            )

        except Exception as e:
            error_msg = f"转换失败: {str(e)}"
            if ctx:
                await ctx.error(error_msg)
            logger.exception(f"转换器 {converter.name} 执行失败")
            return ConversionResult(
                success=False,
                task_id="",
                result={},
                output_type="",
                media_ids=[],
                error=error_msg
            )

    async def _create_input_media(self, media_type: Type[AbstractMedia], content: str) -> AbstractMedia:
        """根据媒体类型创建输入媒体对象"""
        if media_type == TextMedia:
            return TextMedia(content=content)
        elif media_type == ImageMedia:
            # 假设 content 是图像文件路径或 base64 数据
            media = ImageMedia()
            media.load(content)
            return media
        elif media_type == AudioMedia:
            # 假设 content 是音频文件路径
            media = AudioMedia()
            media.load(content)
            return media
        else:
            raise ValueError(f"不支持的输入媒体类型: {media_type}")

    async def _save_output_media(self, media: AbstractMedia, user_id: str = "mcp_user", ctx: Context = None) -> str:
        """保存输出媒体到文件系统和数据库"""
        try:
            from ...config import settings
            from ...database import get_session
            from ...models.media import Media

            if ctx:
                await ctx.info("正在保存输出文件...")

            # 保存媒体文件
            saved_path = media.save(settings.MEDIA_STORAGE_PATH)

            # 保存到数据库
            metadata = media.get_metadata()

            # 确定媒体类型
            media_type = type(media).__name__.replace("Media", "").lower()

            # 创建数据库记录
            with next(get_session()) as session:
                db_media = Media(
                    id=metadata.id,
                    user_id=user_id,
                    media_type=media_type,
                    file_path=saved_path,
                    content_type=metadata.content_type,
                    metadatas=metadata.metadatas
                )
                session.add(db_media)
                session.commit()

            if ctx:
                await ctx.info(f"文件已保存，媒体ID: {metadata.id}")

            return metadata.id  # 返回媒体 ID 而不是文件路径

        except Exception as e:
            error_msg = f"保存输出文件失败: {str(e)}"
            if ctx:
                await ctx.error(error_msg)
            logger.exception("保存输出媒体失败")
            raise ValueError(error_msg)




def create_converter_status_tool(mcp_server: FastMCP):
    """创建转换器状态查询工具"""

    @mcp_server.tool()
    async def list_available_converters(ctx: Context = None) -> Dict[str, Any]:
        """列出所有可用的转换器及其信息"""
        if ctx:
            await ctx.info("正在获取可用转换器列表...")

        converters = ConverterRegistry.get_all()
        converter_info = []

        for converter in converters:
            info = {
                "name": converter.name,
                "description": converter.description,
                "input_types": [t.__name__ for t in converter.input_types],
                "output_types": [t.__name__ for t in converter.output_types]
            }
            converter_info.append(info)

        return {
            "total_converters": len(converter_info),
            "converters": converter_info
        }

    @mcp_server.tool()
    async def get_converter_info(converter_name: str, ctx: Context = None) -> Dict[str, Any]:
        """获取特定转换器的详细信息"""
        if ctx:
            await ctx.info(f"正在获取转换器 {converter_name} 的信息...")

        converters = ConverterRegistry.get_all()

        for converter in converters:
            if converter.name.lower().replace(" ", "_") == converter_name.lower():
                return {
                    "name": converter.name,
                    "description": converter.description,
                    "input_types": [t.__name__ for t in converter.input_types],
                    "output_types": [t.__name__ for t in converter.output_types]
                }

        return {
            "error": f"未找到转换器: {converter_name}"
        }
