"""MCP 服务器主模块

该模块提供了基于 FastMCP 的 MCP 服务器，集成了转换器功能。
"""

import logging
import asyncio
from typing import Optional, Dict, Any
from fastmcp import FastMCP, Context
import threading
import time

from .tools import ConverterToolGenerator, create_converter_status_tool
from ..converters.base import ConverterRegistry

logger = logging.getLogger(__name__)


class MCPServer:
    """MCP 服务器类，管理 FastMCP 实例和转换器工具"""
    
    def __init__(self, name: str = "TransMediaX MCP Server"):
        self.name = name
        self.mcp = FastMCP(name)
        self.tool_generator = ConverterToolGenerator(self.mcp)
        self._setup_server()
        self._monitoring_thread: Optional[threading.Thread] = None
        self._stop_monitoring = False
        
    def _setup_server(self):
        """设置 MCP 服务器"""
        # 添加服务器信息工具
        self._add_server_info_tools()
        
        # 添加转换器状态工具
        create_converter_status_tool(self.mcp)
        
        # 注册转换器工具
        self.refresh_converter_tools()
        
        logger.info(f"MCP 服务器 '{self.name}' 初始化完成")
    
    def _add_server_info_tools(self):
        """添加服务器信息相关的工具"""
        
        @self.mcp.tool()
        async def get_server_info(ctx: Context = None) -> Dict[str, Any]:
            """获取 MCP 服务器信息"""
            if ctx:
                await ctx.info("正在获取服务器信息...")
            
            return {
                "name": self.name,
                "description": "TransMediaX 多媒体转换 MCP 服务器",
                "version": "1.0.0",
                "supported_features": [
                    "文本到图像转换",
                    "文本到音频转换", 
                    "音频到文本转换",
                    "动态转换器发现",
                    "实时状态监控"
                ]
            }
        
        @self.mcp.tool()
        async def health_check(ctx: Context = None) -> Dict[str, Any]:
            """健康检查"""
            if ctx:
                await ctx.info("正在执行健康检查...")
            
            try:
                # 检查转换器注册表
                converters = ConverterRegistry.get_all()
                converter_count = len(converters)
                
                # 检查服务管理器
                from ..services.manager import service_manager
                
                services_status = {
                    "comfyui_client": service_manager.get_comfyui_client() is not None,
                    "llm_client": service_manager.get_llm_client() is not None,
                    "audio_client": service_manager.get_audio_client() is not None
                }
                
                return {
                    "status": "healthy",
                    "timestamp": time.time(),
                    "converter_count": converter_count,
                    "services": services_status
                }
                
            except Exception as e:
                logger.exception("健康检查失败")
                return {
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": time.time()
                }
    
    def refresh_converter_tools(self):
        """刷新转换器工具"""
        try:
            self.tool_generator.register_converter_tools()
            logger.info("转换器工具已刷新")
        except Exception as e:
            logger.exception(f"刷新转换器工具失败: {e}")
    
    def start_monitoring(self, check_interval: int = 30):
        """启动转换器监控线程"""
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            logger.warning("监控线程已在运行")
            return
        
        self._stop_monitoring = False
        self._monitoring_thread = threading.Thread(
            target=self._monitor_converters,
            args=(check_interval,),
            daemon=True
        )
        self._monitoring_thread.start()
        logger.info(f"已启动转换器监控，检查间隔: {check_interval}秒")
    
    def stop_monitoring(self):
        """停止转换器监控"""
        self._stop_monitoring = True
        if self._monitoring_thread:
            self._monitoring_thread.join(timeout=5)
        logger.info("转换器监控已停止")
    
    def _monitor_converters(self, check_interval: int):
        """监控转换器变化的后台线程"""
        last_converter_count = len(ConverterRegistry.get_all())
        
        while not self._stop_monitoring:
            try:
                current_converter_count = len(ConverterRegistry.get_all())
                
                if current_converter_count != last_converter_count:
                    logger.info(f"检测到转换器数量变化: {last_converter_count} -> {current_converter_count}")
                    self.refresh_converter_tools()
                    last_converter_count = current_converter_count
                
                time.sleep(check_interval)
                
            except Exception as e:
                logger.exception(f"监控转换器时发生错误: {e}")
                time.sleep(check_interval)
    
    def run_stdio(self):
        """以 STDIO 模式运行 MCP 服务器"""
        logger.info(f"以 STDIO 模式启动 MCP 服务器: {self.name}")
        self.mcp.run(transport="stdio")
    
    def run_sse(self, host: str = "127.0.0.1", port: int = 8001, path: str = "/mcp"):
        """以 SSE 模式运行 MCP 服务器"""
        logger.info(f"以 SSE 模式启动 MCP 服务器: {self.name} 在 {host}:{port}{path}")
        self.mcp.run(transport="sse", host=host, port=port, path=path)
    
    def run_streamable_http(self, host: str = "127.0.0.1", port: int = 8001, path: str = "/mcp"):
        """以 Streamable HTTP 模式运行 MCP 服务器"""
        logger.info(f"以 Streamable HTTP 模式启动 MCP 服务器: {self.name} 在 {host}:{port}{path}")
        self.mcp.run(transport="streamable-http", host=host, port=port, path=path)
    
    def get_fastmcp_instance(self) -> FastMCP:
        """获取 FastMCP 实例，用于集成到其他应用中"""
        return self.mcp
    
    async def handle_mcp_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理 MCP 请求（用于集成到 FastAPI 中）"""
        # 这个方法可以用于将 MCP 请求集成到 FastAPI 路由中
        # 具体实现取决于 FastMCP 的内部 API
        try:
            # 这里需要根据 FastMCP 的实际 API 来实现
            # 目前作为占位符
            return {"status": "success", "message": "MCP request handled"}
        except Exception as e:
            logger.exception("处理 MCP 请求失败")
            return {"status": "error", "message": str(e)}


# 全局 MCP 服务器实例
_mcp_server_instance: Optional[MCPServer] = None


def get_mcp_server() -> MCPServer:
    """获取全局 MCP 服务器实例"""
    global _mcp_server_instance
    if _mcp_server_instance is None:
        _mcp_server_instance = MCPServer()
    return _mcp_server_instance


def initialize_mcp_server(name: str = "TransMediaX MCP Server") -> MCPServer:
    """初始化 MCP 服务器"""
    global _mcp_server_instance
    _mcp_server_instance = MCPServer(name)
    return _mcp_server_instance
