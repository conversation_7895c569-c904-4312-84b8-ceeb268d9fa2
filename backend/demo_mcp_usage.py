#!/usr/bin/env python3
"""
MCP 使用演示脚本

该脚本演示如何使用 FastMCP 客户端连接到 TransMediaX MCP 服务器并调用转换工具。
"""

import asyncio
import logging
import sys
import os
import json

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastmcp import Client
from app.config import settings
from app.core.services.manager import service_manager
from app.core.mcp.server import initialize_mcp_server
from app.database import create_db_and_tables

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


async def demo_server_info(client: Client):
    """演示获取服务器信息"""
    print("\n" + "="*50)
    print("📋 获取服务器信息")
    print("="*50)
    
    try:
        # 获取服务器信息
        result = await client.call_tool("get_server_info", {})
        server_info = json.loads(result[0].text)
        
        print(f"🖥️  服务器名称: {server_info['name']}")
        print(f"📝 描述: {server_info['description']}")
        print(f"🔢 版本: {server_info['version']}")
        print("🎯 支持的功能:")
        for feature in server_info['supported_features']:
            print(f"   • {feature}")
        
        # 健康检查
        result = await client.call_tool("health_check", {})
        health_info = json.loads(result[0].text)
        
        print(f"\n💚 健康状态: {health_info['status']}")
        print(f"🔧 转换器数量: {health_info['converter_count']}")
        print("🔌 服务状态:")
        for service, status in health_info['services'].items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {service}: {'可用' if status else '不可用'}")
        
    except Exception as e:
        print(f"❌ 获取服务器信息失败: {e}")


async def demo_list_converters(client: Client):
    """演示列出可用转换器"""
    print("\n" + "="*50)
    print("🔧 可用转换器列表")
    print("="*50)
    
    try:
        result = await client.call_tool("list_available_converters", {})
        converters_info = json.loads(result[0].text)
        
        print(f"📊 总计: {converters_info['total_converters']} 个转换器\n")
        
        for i, converter in enumerate(converters_info['converters'], 1):
            print(f"{i}. 🎯 {converter['name']}")
            print(f"   📝 描述: {converter['description']}")
            print(f"   📥 输入类型: {', '.join(converter['input_types'])}")
            print(f"   📤 输出类型: {', '.join(converter['output_types'])}")
            print()
        
    except Exception as e:
        print(f"❌ 获取转换器列表失败: {e}")


async def demo_list_tools(client: Client):
    """演示列出所有可用工具"""
    print("\n" + "="*50)
    print("🛠️  可用 MCP 工具")
    print("="*50)
    
    try:
        tools = await client.list_tools()
        
        print(f"📊 总计: {len(tools)} 个工具\n")
        
        # 分类显示工具
        converter_tools = [tool for tool in tools if tool.name.startswith("convert_")]
        system_tools = [tool for tool in tools if not tool.name.startswith("convert_")]
        
        if converter_tools:
            print("🔄 转换器工具:")
            for tool in converter_tools:
                print(f"   • {tool.name}")
                if hasattr(tool, 'description') and tool.description:
                    print(f"     {tool.description}")
            print()
        
        if system_tools:
            print("⚙️  系统工具:")
            for tool in system_tools:
                print(f"   • {tool.name}")
                if hasattr(tool, 'description') and tool.description:
                    print(f"     {tool.description}")
            print()
        
    except Exception as e:
        print(f"❌ 获取工具列表失败: {e}")


async def demo_text_to_image_conversion(client: Client):
    """演示文本到图像转换"""
    print("\n" + "="*50)
    print("🎨 文本到图像转换演示")
    print("="*50)
    
    try:
        # 检查是否有文本到图像转换工具
        tools = await client.list_tools()
        if not any(tool.name == "convert_text_to_image" for tool in tools):
            print("❌ 文本到图像转换工具不可用")
            return
        
        print("🚀 开始转换...")
        print("📝 输入文本: '一只可爱的小猫坐在花园里'")
        
        result = await client.call_tool("convert_text_to_image", {
            "input_content": "一只可爱的小猫坐在花园里",
            "parameters": {
                "width": 512,
                "height": 512,
                "num_images": 1,
                "positive_prompt": "cute cat sitting in a garden, high quality, detailed",
                "negative_prompt": "blurry, low quality, distorted"
            }
        })
        
        conversion_result = json.loads(result[0].text)
        
        if conversion_result['success']:
            print("✅ 转换成功!")
            print(f"🆔 任务ID: {conversion_result['task_id']}")
            print(f"📤 输出类型: {conversion_result['output_type']}")
            
            if 'result' in conversion_result and 'file_path' in conversion_result['result']:
                print(f"📁 输出文件: {conversion_result['result']['file_path']}")
            
            print("📊 结果详情:")
            print(json.dumps(conversion_result['result'], indent=2, ensure_ascii=False))
        else:
            print(f"❌ 转换失败: {conversion_result['error']}")
        
    except Exception as e:
        print(f"❌ 文本到图像转换演示失败: {e}")


async def demo_interactive_mode(client: Client):
    """交互式演示模式"""
    print("\n" + "="*50)
    print("🎮 交互式模式")
    print("="*50)
    print("输入 'help' 查看可用命令，输入 'quit' 退出")
    
    while True:
        try:
            command = input("\n🔧 请输入命令: ").strip().lower()
            
            if command == 'quit':
                print("👋 再见!")
                break
            elif command == 'help':
                print("\n可用命令:")
                print("  info     - 获取服务器信息")
                print("  health   - 健康检查")
                print("  converters - 列出转换器")
                print("  tools    - 列出所有工具")
                print("  convert  - 文本到图像转换")
                print("  help     - 显示此帮助")
                print("  quit     - 退出")
            elif command == 'info':
                await demo_server_info(client)
            elif command == 'health':
                result = await client.call_tool("health_check", {})
                health_info = json.loads(result[0].text)
                print(f"💚 健康状态: {health_info['status']}")
            elif command == 'converters':
                await demo_list_converters(client)
            elif command == 'tools':
                await demo_list_tools(client)
            elif command == 'convert':
                await demo_text_to_image_conversion(client)
            else:
                print(f"❌ 未知命令: {command}，输入 'help' 查看可用命令")
                
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 执行命令时发生错误: {e}")


async def main():
    """主演示函数"""
    print("🚀 TransMediaX MCP 服务器演示")
    print("="*50)
    
    try:
        # 设置环境
        print("⚙️  正在设置环境...")
        create_db_and_tables()
        os.makedirs(settings.MEDIA_STORAGE_PATH, exist_ok=True)
        service_manager.register_converters()
        
        # 初始化 MCP 服务器
        print("🖥️  正在初始化 MCP 服务器...")
        mcp_server = initialize_mcp_server("TransMediaX Demo MCP Server")
        
        # 使用内存传输连接
        print("🔗 正在连接到 MCP 服务器...")
        async with Client(mcp_server.get_fastmcp_instance()) as client:
            print("✅ 连接成功!")
            
            # 运行演示
            await demo_server_info(client)
            await demo_list_converters(client)
            await demo_list_tools(client)
            await demo_text_to_image_conversion(client)
            
            # 进入交互模式
            await demo_interactive_mode(client)
            
    except Exception as e:
        logger.exception(f"演示失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
