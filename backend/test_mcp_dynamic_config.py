#!/usr/bin/env python3
"""
测试 MCP 动态配置功能

该脚本测试修改 MCP 配置后，工具参数是否会动态更新。
"""

import asyncio
import sys
import os
import json

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastmcp import Client
from app.config import settings
from app.core.services.manager import service_manager
from app.core.mcp.server import initialize_mcp_server
from app.database import create_db_and_tables


async def test_dynamic_config():
    """测试动态配置功能"""
    print("🧪 测试 MCP 动态配置功能")
    print("=" * 50)
    
    # 设置环境
    create_db_and_tables()
    os.makedirs(settings.MEDIA_STORAGE_PATH, exist_ok=True)
    service_manager.register_converters()
    
    print("📋 原始配置:")
    print(f"  TTS Voices: {settings.MCP_TTS_VOICES}")
    print(f"  TTS Models: {settings.MCP_TTS_MODELS}")
    print(f"  Image Size Range: {settings.MCP_IMAGE_MIN_SIZE}-{settings.MCP_IMAGE_MAX_SIZE}")
    
    # 初始化 MCP 服务器
    mcp_server = initialize_mcp_server('Dynamic Config Test Server')
    
    async with Client(mcp_server.get_fastmcp_instance()) as client:
        print("\n🔧 测试原始配置的工具...")
        
        # 测试文本到音频转换工具
        try:
            result = await client.call_tool('text_to_audio_converter', {
                'text': '测试音频',
                'parameters': {
                    'voice': 'alloy',  # 使用原始配置中的语音
                    'model': 'tts-1'   # 使用原始配置中的模型
                }
            })
            print("✅ 原始配置工具调用成功")
        except Exception as e:
            print(f"❌ 原始配置工具调用失败: {e}")
    
    print("\n🔄 修改配置...")
    
    # 修改配置
    settings.MCP_TTS_VOICES = ["custom_voice_1", "custom_voice_2", "custom_voice_3"]
    settings.MCP_TTS_MODELS = ["custom_tts_model"]
    settings.MCP_IMAGE_MIN_SIZE = 128
    settings.MCP_IMAGE_MAX_SIZE = 1024
    settings.MCP_IMAGE_DEFAULT_SIZE = 256
    
    print("📋 新配置:")
    print(f"  TTS Voices: {settings.MCP_TTS_VOICES}")
    print(f"  TTS Models: {settings.MCP_TTS_MODELS}")
    print(f"  Image Size Range: {settings.MCP_IMAGE_MIN_SIZE}-{settings.MCP_IMAGE_MAX_SIZE}")
    
    # 刷新 MCP 工具
    print("\n🔄 刷新 MCP 工具...")
    mcp_server.refresh_converter_tools()
    
    async with Client(mcp_server.get_fastmcp_instance()) as client:
        print("\n🔧 测试新配置的工具...")
        
        # 获取工具列表
        tools = await client.list_tools()
        print(f"工具数量: {len(tools)}")
        
        # 查看文本到音频工具的参数
        for tool in tools:
            if tool.name == 'text_to_audio_converter':
                print(f"\n📝 工具: {tool.name}")
                print("描述片段:")
                description_lines = tool.description.split('\n')[:10]
                for line in description_lines:
                    if line.strip():
                        print(f"  {line.strip()}")
                break
        
        # 测试使用新配置的工具
        try:
            result = await client.call_tool('text_to_audio_converter', {
                'text': '测试新配置音频',
                'parameters': {
                    'voice': 'custom_voice_1',  # 使用新配置中的语音
                    'model': 'custom_tts_model'  # 使用新配置中的模型
                }
            })
            print("✅ 新配置工具调用成功")
        except Exception as e:
            print(f"❌ 新配置工具调用失败: {e}")
            # 这是预期的，因为我们使用了不存在的模型
            if "Model not found" in str(e) or "转换失败" in str(e):
                print("  (这是预期的错误，因为使用了自定义模型名称)")
        
        # 测试图像生成工具的参数范围
        try:
            result = await client.call_tool('text_to_image_converter', {
                'text': '测试图像',
                'parameters': {
                    'width': 256,   # 使用新的默认尺寸
                    'height': 256,
                    'num_images': 1
                }
            })
            print("✅ 新图像尺寸参数工具调用成功")
        except Exception as e:
            print(f"❌ 新图像尺寸参数工具调用失败: {e}")
            if "ComfyUI" in str(e) or "转换失败" in str(e):
                print("  (这是预期的错误，因为 ComfyUI 服务未运行)")
    
    print("\n🎉 动态配置测试完成!")
    print("✅ MCP 工具参数已成功使用动态配置")


if __name__ == "__main__":
    asyncio.run(test_dynamic_config())
