# TransMediaX MCP 服务器

TransMediaX 现在支持 Model Context Protocol (MCP) 服务器功能，可以将多媒体转换功能作为 MCP 工具提供给 LLM 应用程序使用。

## 功能特性

- **自动工具生成**: 根据 `backend/app/core/converters` 目录中的转换器自动生成 MCP 工具
- **动态更新**: 当转换器发生变化时，MCP 工具会自动更新
- **多种传输方式**: 支持 STDIO、SSE 和 Streamable HTTP 传输
- **实时监控**: 可选的转换器变化监控功能
- **完整集成**: 与现有 FastAPI 应用无缝集成

## 配置

MCP 服务器的配置在 `backend/app/config.py` 中：

```python
# MCP Server settings
MCP_ENABLED: bool = True                    # 是否启用 MCP 服务器
MCP_SERVER_NAME: str = "TransMediaX MCP Server"  # 服务器名称
MCP_TRANSPORT: str = "sse"                  # 传输方式: stdio, sse, streamable-http
MCP_HOST: str = "127.0.0.1"                # 服务器主机
MCP_PORT: int = 8001                       # 服务器端口
MCP_PATH: str = "/mcp"                     # MCP 路径
MCP_MONITORING_ENABLED: bool = True        # 是否启用监控
MCP_MONITORING_INTERVAL: int = 30          # 监控间隔（秒）
```

## 使用方法

### 1. 与 FastAPI 集成运行

启动 FastAPI 应用时，MCP 服务器会自动在后台线程中启动（除非配置为 STDIO 模式）：

```bash
cd backend
python -m app.main
```

### 2. 独立运行 MCP 服务器

使用提供的独立脚本：

```bash
cd backend

# STDIO 模式（默认）
python mcp_server.py

# SSE 模式
python mcp_server.py --transport sse --host 127.0.0.1 --port 8001

# Streamable HTTP 模式
python mcp_server.py --transport streamable-http --host 127.0.0.1 --port 8001

# 查看所有选项
python mcp_server.py --help
```

### 3. 测试 MCP 服务器

使用测试客户端验证功能：

```bash
cd backend

# 测试内存模式（直接连接）
python test_mcp_client.py --transport memory

# 测试 SSE 模式
python test_mcp_client.py --transport sse --url http://127.0.0.1:8001/mcp

# 测试 STDIO 模式
python test_mcp_client.py --transport stdio --server-script mcp_server.py
```

## 可用的 MCP 工具

MCP 服务器会自动为每个转换器生成对应的工具：

### 转换器工具

- `convert_text_to_image`: 文本到图像转换
- `convert_text_to_audio`: 文本到音频转换
- `convert_audio_to_text`: 音频到文本转换

每个转换器工具接受以下参数：
- `input_content`: 输入内容（文本、文件路径等）
- `parameters`: 转换参数（可选）

### 系统工具

- `get_server_info`: 获取服务器信息
- `health_check`: 健康检查
- `list_available_converters`: 列出所有可用转换器
- `get_converter_info`: 获取特定转换器信息

## 工具使用示例

### 文本到图像转换

```python
result = await client.call_tool("convert_text_to_image", {
    "input_content": "一只可爱的小猫",
    "parameters": {
        "width": 512,
        "height": 512,
        "num_images": 1,
        "positive_prompt": "cute cat, high quality",
        "negative_prompt": "blurry, low quality"
    }
})
```

### 文本到音频转换

```python
result = await client.call_tool("convert_text_to_audio", {
    "input_content": "你好，这是一个测试音频。",
    "parameters": {
        "voice": "alloy",
        "speed": 1.0,
        "model": "tts-1"
    }
})
```

### 音频到文本转换

```python
result = await client.call_tool("convert_audio_to_text", {
    "input_content": "/path/to/audio/file.mp3",
    "parameters": {
        "model": "whisper-1",
        "language": "zh"
    }
})
```

## 开发指南

### 添加新的转换器

1. 在 `backend/app/core/converters/` 目录中创建新的转换器类
2. 继承 `AbstractConverter` 基类
3. 实现必要的方法和属性
4. 在转换器注册函数中注册新转换器
5. MCP 服务器会自动检测并生成对应的工具

### 自定义 MCP 工具

可以在 `backend/app/core/mcp/tools.py` 中添加自定义的 MCP 工具：

```python
@mcp_server.tool()
async def custom_tool(param1: str, param2: int, ctx: Context = None) -> Dict[str, Any]:
    """自定义工具描述"""
    if ctx:
        await ctx.info("执行自定义工具...")

    # 工具逻辑
    return {"result": "success"}
```

## 故障排除

### 常见问题

1. **MCP 服务器无法启动**
   - 检查端口是否被占用
   - 确认 FastMCP 依赖已正确安装
   - 查看日志输出获取详细错误信息

2. **转换器工具未出现**
   - 确认转换器已正确注册
   - 检查转换器类是否继承了 `AbstractConverter`
   - 启用监控功能以自动检测变化

3. **工具执行失败**
   - 检查输入参数格式是否正确
   - 确认相关服务（ComfyUI、LLM、Audio API）配置正确
   - 查看服务器日志获取详细错误信息

### 日志调试

启用详细日志输出：

```bash
python mcp_server.py --verbose
python test_mcp_client.py --verbose
```

## 快速开始

### 1. 安装依赖

确保已安装所有依赖：

```bash
cd backend
pip install -r requirements.txt
```

### 2. 运行演示

```bash
# 运行交互式演示
python demo_mcp_usage.py

# 运行测试客户端
python test_mcp_client.py --transport memory
```

### 3. 启动独立 MCP 服务器

```bash
# STDIO 模式（用于 Claude Desktop 等客户端）
python mcp_server.py

# Web 模式（用于 HTTP 客户端）
python mcp_server.py --transport sse --port 8001
```

## 成功验证

✅ **MCP 服务器集成完成**：
- 自动为每个转换器生成 MCP 工具
- 支持动态转换器发现和更新
- 提供完整的服务器信息和健康检查
- 支持多种传输方式（STDIO、SSE、Streamable HTTP）

✅ **测试验证通过**：
- 内存模式连接测试成功
- 工具注册和调用正常工作
- 服务器信息和状态查询功能正常
- 转换器列表和详情查询功能正常

✅ **文档和示例完整**：
- 提供了详细的使用文档
- 包含交互式演示脚本
- 提供了测试客户端
- 支持独立运行模式

## 依赖要求

- FastMCP >= 2.5.1
- FastAPI
- 其他 TransMediaX 依赖

确保在 `backend/requirements.txt` 中包含了 `fastmcp==2.5.1`。
