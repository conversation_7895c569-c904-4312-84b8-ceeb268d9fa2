<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TransMediaX - Admin</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">TransMediaX</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="admin.html">Admin</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1>Admin Configuration</h1>

        <div id="loginForm" class="card mt-4">
            <div class="card-header">
                <h5>Admin Login</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="username">
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" class="form-control" id="password">
                </div>
                <button id="loginBtn" class="btn btn-primary">Login</button>
                <div id="loginError" class="text-danger mt-2 d-none">Invalid credentials</div>
            </div>
        </div>

        <div id="adminPanel" class="d-none">
            <div class="card mt-4">
                <div class="card-header">
                    <h5>API Configuration</h5>
                </div>
                <div class="card-body">
                    <h6 class="mb-3">Speech to Text Service (OpenAI Compatible)</h6>
                    <div class="mb-3">
                        <label for="sttApiKey" class="form-label">STT API Key</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="sttApiKey">
                            <button class="btn btn-outline-secondary" type="button" onclick="document.getElementById('sttApiKey').value = ''">清除</button>
                        </div>
                        <small class="form-text text-muted">如需清除API密钥，请点击清除按钮后保存</small>
                    </div>
                    <div class="mb-3">
                        <label for="sttBaseUrl" class="form-label">STT Base URL</label>
                        <input type="text" class="form-control" id="sttBaseUrl">
                    </div>
                    <div class="mb-3">
                        <label for="sttModel" class="form-label">STT Model</label>
                        <input type="text" class="form-control" id="sttModel">
                    </div>

                    <h6 class="mb-3">Text to Speech Service (OpenAI Compatible)</h6>
                    <div class="mb-3">
                        <label for="ttsApiKey" class="form-label">TTS API Key</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="ttsApiKey">
                            <button class="btn btn-outline-secondary" type="button" onclick="document.getElementById('ttsApiKey').value = ''">清除</button>
                        </div>
                        <small class="form-text text-muted">如需清除API密钥，请点击清除按钮后保存</small>
                    </div>
                    <div class="mb-3">
                        <label for="ttsBaseUrl" class="form-label">TTS Base URL</label>
                        <input type="text" class="form-control" id="ttsBaseUrl">
                    </div>
                    <div class="mb-3">
                        <label for="ttsModel" class="form-label">TTS Model</label>
                        <input type="text" class="form-control" id="ttsModel">
                    </div>

                    <h6 class="mb-3">LLM Service (OpenAI Compatible)</h6>
                    <div class="mb-3">
                        <label for="llmApiKey" class="form-label">LLM API Key</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="llmApiKey">
                            <button class="btn btn-outline-secondary" type="button" onclick="document.getElementById('llmApiKey').value = ''">清除</button>
                        </div>
                        <small class="form-text text-muted">如需清除API密钥，请点击清除按钮后保存</small>
                    </div>
                    <div class="mb-3">
                        <label for="llmBaseUrl" class="form-label">LLM Base URL</label>
                        <input type="text" class="form-control" id="llmBaseUrl">
                    </div>
                    <div class="mb-3">
                        <label for="llmModel" class="form-label">LLM Model</label>
                        <input type="text" class="form-control" id="llmModel">
                    </div>

                    <h6 class="mb-3">Image Generation Service</h6>
                    <div class="mb-3">
                        <label for="comfyuiBaseUrl" class="form-label">ComfyUI Base URL</label>
                        <input type="text" class="form-control" id="comfyuiBaseUrl">
                    </div>
                    <button id="saveConfigBtn" class="btn btn-primary">Save Configuration</button>
                    <div id="configSaveSuccess" class="text-success mt-2 d-none">Configuration saved successfully</div>
                    <div id="configSaveError" class="text-danger mt-2 d-none">Error saving configuration</div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5>MCP Tool Configuration</h5>
                </div>
                <div class="card-body">
                    <h6 class="mb-3">Text to Speech (TTS) Settings</h6>
                    <div class="mb-3">
                        <label for="mcpTtsVoices" class="form-label">Available Voices</label>
                        <input type="text" class="form-control" id="mcpTtsVoices" placeholder="alloy,echo,fable,onyx,nova,shimmer">
                        <small class="form-text text-muted">用逗号分隔多个语音类型</small>
                    </div>
                    <div class="mb-3">
                        <label for="mcpTtsModels" class="form-label">Available Models</label>
                        <input type="text" class="form-control" id="mcpTtsModels" placeholder="tts-1,tts-1-hd">
                        <small class="form-text text-muted">用逗号分隔多个模型</small>
                    </div>
                    <div class="mb-3">
                        <label for="mcpTtsFormats" class="form-label">Available Formats</label>
                        <input type="text" class="form-control" id="mcpTtsFormats" placeholder="mp3,opus,aac,flac">
                        <small class="form-text text-muted">用逗号分隔多个音频格式</small>
                    </div>

                    <h6 class="mb-3">Speech to Text (STT) Settings</h6>
                    <div class="mb-3">
                        <label for="mcpSttModels" class="form-label">Available Models</label>
                        <input type="text" class="form-control" id="mcpSttModels" placeholder="whisper-1">
                        <small class="form-text text-muted">用逗号分隔多个模型</small>
                    </div>
                    <div class="mb-3">
                        <label for="mcpSttResponseFormats" class="form-label">Response Formats</label>
                        <input type="text" class="form-control" id="mcpSttResponseFormats" placeholder="json,text,srt,verbose_json,vtt">
                        <small class="form-text text-muted">用逗号分隔多个响应格式</small>
                    </div>

                    <h6 class="mb-3">Image Generation Settings</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="mcpImageMinSize" class="form-label">Min Image Size</label>
                                <input type="number" class="form-control" id="mcpImageMinSize" min="64" max="2048">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="mcpImageMaxSize" class="form-label">Max Image Size</label>
                                <input type="number" class="form-control" id="mcpImageMaxSize" min="64" max="2048">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="mcpImageDefaultSize" class="form-label">Default Size</label>
                                <input type="number" class="form-control" id="mcpImageDefaultSize" min="64" max="2048">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="mcpImageMaxCount" class="form-label">Max Image Count</label>
                        <input type="number" class="form-control" id="mcpImageMaxCount" min="1" max="10">
                    </div>

                    <h6 class="mb-3">Parameter Ranges</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mcpSpeedMin" class="form-label">Speed Min</label>
                                <input type="number" class="form-control" id="mcpSpeedMin" step="0.1" min="0.1" max="4.0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mcpSpeedMax" class="form-label">Speed Max</label>
                                <input type="number" class="form-control" id="mcpSpeedMax" step="0.1" min="0.1" max="4.0">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mcpCfgScaleMin" class="form-label">CFG Scale Min</label>
                                <input type="number" class="form-control" id="mcpCfgScaleMin" step="0.1" min="1.0" max="20.0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mcpCfgScaleMax" class="form-label">CFG Scale Max</label>
                                <input type="number" class="form-control" id="mcpCfgScaleMax" step="0.1" min="1.0" max="20.0">
                            </div>
                        </div>
                    </div>

                    <button id="saveMcpConfigBtn" class="btn btn-primary">Save MCP Configuration</button>
                    <div id="mcpConfigSaveSuccess" class="text-success mt-2 d-none">MCP configuration saved successfully</div>
                    <div id="mcpConfigSaveError" class="text-danger mt-2 d-none">Error saving MCP configuration</div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5>Available Converters</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Input Types</th>
                                <th>Output Types</th>
                            </tr>
                        </thead>
                        <tbody id="convertersTable">
                            <!-- Converters will be added here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5>System Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Database Path</label>
                        <input type="text" class="form-control" id="databasePath" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Media Storage Path</label>
                        <input type="text" class="form-control" id="mediaStoragePath" readonly>
                    </div>
                    <button id="restartBtn" class="btn btn-danger">Restart Server</button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/api.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>
