from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Header
from typing import Optional
import secrets

from ....config import settings

router = APIRouter()


def verify_admin(authorization: Optional[str] = Header(None)):
    """Verify admin credentials."""
    if not authorization or not authorization.startswith('Basic '):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )

    try:
        import base64
        decoded = base64.b64decode(authorization[6:]).decode('utf-8')
        username, password = decoded.split(':', 1)
        correct_username = secrets.compare_digest(username, settings.ADMIN_USERNAME)
        correct_password = secrets.compare_digest(password, settings.ADMIN_PASSWORD)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )

    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )

    return True


@router.get("/config/")
def get_config(authorization: Optional[str] = Header(None)):
    """Get the current configuration."""
    # Verify admin credentials
    verify_admin(authorization)
    config = {
        "PROJECT_NAME": settings.PROJECT_NAME,
        "API_V1_STR": settings.API_V1_STR,
        "DATABASE_PATH": settings.DATABASE_PATH,
        "MEDIA_STORAGE_PATH": settings.MEDIA_STORAGE_PATH,

        # Speech to Text service
        "STT_API_KEY": settings.STT_API_KEY and "********",  # Mask API key
        "STT_BASE_URL": settings.STT_BASE_URL,
        "STT_MODEL": settings.STT_MODEL,

        # Text to Speech service
        "TTS_API_KEY": settings.TTS_API_KEY and "********",  # Mask API key
        "TTS_BASE_URL": settings.TTS_BASE_URL,
        "TTS_MODEL": settings.TTS_MODEL,

        # LLM service
        "LLM_API_KEY": settings.LLM_API_KEY and "********",  # Mask API key
        "LLM_BASE_URL": settings.LLM_BASE_URL,
        "LLM_MODEL": settings.LLM_MODEL,

        # Image generation service
        "COMFYUI_BASE_URL": settings.COMFYUI_BASE_URL,

        # 兼容旧版本
        "AUDIO_API_KEY": settings.AUDIO_API_KEY and "********",  # Mask API key
        "AUDIO_BASE_URL": settings.AUDIO_BASE_URL,
    }

    return config


@router.post("/config/")
def update_config(config: Dict[str, Any], authorization: Optional[str] = Header(None)):
    """Update the configuration."""
    # Verify admin credentials
    verify_admin(authorization)

    # API密钥字段列表
    api_key_fields = ["STT_API_KEY", "TTS_API_KEY", "LLM_API_KEY", "AUDIO_API_KEY"]

    # 更新设置
    for key, value in config.items():
        if hasattr(settings, key) and key != "SECRET_KEY":
            # 如果是API密钥字段且值为掩码（星号），则保持原值不变
            if key in api_key_fields and value == "********":
                continue
            setattr(settings, key, value)

    # 保存设置到文件
    settings.save_to_file()

    return {"message": "Configuration updated successfully"}


@router.post("/restart/")
def restart_server(authorization: Optional[str] = Header(None)):
    """Restart the server (not implemented)."""
    # Verify admin credentials
    verify_admin(authorization)
    # In a real implementation, this would restart the server
    # For now, we'll just return a message
    return {"message": "Server restart not implemented in this version"}


@router.get("/converters/")
def list_converters(authorization: Optional[str] = Header(None)):
    """List all registered converters."""
    # Verify admin credentials
    verify_admin(authorization)
    from ....core.converters.base import ConverterRegistry

    converters = []
    for converter in ConverterRegistry.get_all():
        converters.append({
            "name": converter.name,
            "description": converter.description,
            "input_types": [t.__name__ for t in converter.input_types],
            "output_types": [t.__name__ for t in converter.output_types],
        })

    return converters


@router.get("/converter-config/")
def get_converter_config(authorization: Optional[str] = Header(None)):
    """Get global converter configuration (used by both API and MCP)."""
    # Verify admin credentials
    verify_admin(authorization)

    converter_config = {
        # MCP Server settings
        "MCP_ENABLED": settings.MCP_ENABLED,
        "MCP_SERVER_NAME": settings.MCP_SERVER_NAME,
        "MCP_TRANSPORT": settings.MCP_TRANSPORT,
        "MCP_HOST": settings.MCP_HOST,
        "MCP_PORT": settings.MCP_PORT,
        "MCP_PATH": settings.MCP_PATH,
        "MCP_MONITORING_ENABLED": settings.MCP_MONITORING_ENABLED,
        "MCP_MONITORING_INTERVAL": settings.MCP_MONITORING_INTERVAL,

        # Text-to-Speech (TTS) Settings
        "TTS_AVAILABLE_VOICES": settings.TTS_AVAILABLE_VOICES,
        "TTS_AVAILABLE_MODELS": settings.TTS_AVAILABLE_MODELS,
        "TTS_AVAILABLE_FORMATS": settings.TTS_AVAILABLE_FORMATS,
        "TTS_DEFAULT_VOICE": settings.TTS_DEFAULT_VOICE,
        "TTS_DEFAULT_MODEL": settings.TTS_DEFAULT_MODEL,
        "TTS_DEFAULT_FORMAT": settings.TTS_DEFAULT_FORMAT,
        "TTS_SPEED_MIN": settings.TTS_SPEED_MIN,
        "TTS_SPEED_MAX": settings.TTS_SPEED_MAX,
        "TTS_SPEED_DEFAULT": settings.TTS_SPEED_DEFAULT,

        # Speech-to-Text (STT) Settings
        "STT_AVAILABLE_MODELS": settings.STT_AVAILABLE_MODELS,
        "STT_AVAILABLE_RESPONSE_FORMATS": settings.STT_AVAILABLE_RESPONSE_FORMATS,
        "STT_DEFAULT_MODEL": settings.STT_DEFAULT_MODEL,
        "STT_DEFAULT_RESPONSE_FORMAT": settings.STT_DEFAULT_RESPONSE_FORMAT,
        "STT_TEMPERATURE_MIN": settings.STT_TEMPERATURE_MIN,
        "STT_TEMPERATURE_MAX": settings.STT_TEMPERATURE_MAX,
        "STT_TEMPERATURE_DEFAULT": settings.STT_TEMPERATURE_DEFAULT,

        # Image Generation Settings
        "IMAGE_SIZE_MIN": settings.IMAGE_SIZE_MIN,
        "IMAGE_SIZE_MAX": settings.IMAGE_SIZE_MAX,
        "IMAGE_SIZE_DEFAULT": settings.IMAGE_SIZE_DEFAULT,
        "IMAGE_COUNT_MAX": settings.IMAGE_COUNT_MAX,
        "IMAGE_COUNT_DEFAULT": settings.IMAGE_COUNT_DEFAULT,
        "IMAGE_CFG_SCALE_MIN": settings.IMAGE_CFG_SCALE_MIN,
        "IMAGE_CFG_SCALE_MAX": settings.IMAGE_CFG_SCALE_MAX,
        "IMAGE_CFG_SCALE_DEFAULT": settings.IMAGE_CFG_SCALE_DEFAULT,
        "IMAGE_STEPS_MIN": settings.IMAGE_STEPS_MIN,
        "IMAGE_STEPS_MAX": settings.IMAGE_STEPS_MAX,
        "IMAGE_STEPS_DEFAULT": settings.IMAGE_STEPS_DEFAULT,
    }

    return converter_config


@router.post("/converter-config/")
def update_converter_config(config: Dict[str, Any], authorization: Optional[str] = Header(None)):
    """Update global converter configuration (used by both API and MCP)."""
    # Verify admin credentials
    verify_admin(authorization)

    # 允许更新的配置键
    allowed_keys = [
        # MCP Server settings
        "MCP_ENABLED", "MCP_SERVER_NAME", "MCP_TRANSPORT", "MCP_HOST", "MCP_PORT", "MCP_PATH",
        "MCP_MONITORING_ENABLED", "MCP_MONITORING_INTERVAL",
        # TTS Settings
        "TTS_AVAILABLE_VOICES", "TTS_AVAILABLE_MODELS", "TTS_AVAILABLE_FORMATS",
        "TTS_DEFAULT_VOICE", "TTS_DEFAULT_MODEL", "TTS_DEFAULT_FORMAT",
        "TTS_SPEED_MIN", "TTS_SPEED_MAX", "TTS_SPEED_DEFAULT",
        # STT Settings
        "STT_AVAILABLE_MODELS", "STT_AVAILABLE_RESPONSE_FORMATS",
        "STT_DEFAULT_MODEL", "STT_DEFAULT_RESPONSE_FORMAT",
        "STT_TEMPERATURE_MIN", "STT_TEMPERATURE_MAX", "STT_TEMPERATURE_DEFAULT",
        # Image Settings
        "IMAGE_SIZE_MIN", "IMAGE_SIZE_MAX", "IMAGE_SIZE_DEFAULT",
        "IMAGE_COUNT_MAX", "IMAGE_COUNT_DEFAULT",
        "IMAGE_CFG_SCALE_MIN", "IMAGE_CFG_SCALE_MAX", "IMAGE_CFG_SCALE_DEFAULT",
        "IMAGE_STEPS_MIN", "IMAGE_STEPS_MAX", "IMAGE_STEPS_DEFAULT",
    ]

    # 更新设置
    for key, value in config.items():
        if hasattr(settings, key) and key in allowed_keys:
            setattr(settings, key, value)

    # 保存设置到文件
    settings.save_to_file()

    # 刷新 MCP 工具（如果 MCP 服务器正在运行）
    try:
        from ....core.mcp.server import get_mcp_server
        mcp_server = get_mcp_server()
        if mcp_server:
            mcp_server.refresh_converter_tools()
    except Exception as e:
        # MCP 服务器可能未运行，忽略错误
        pass

    return {"message": "Converter configuration updated successfully"}
