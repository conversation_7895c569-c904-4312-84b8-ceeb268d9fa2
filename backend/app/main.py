import os
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import logging

from .config import settings
from .database import create_db_and_tables
from .api.v1.router import api_router
from .core.services.manager import service_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
)

# Set up CORS
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

# Service clients are managed by the ServiceManager

# Mount static files for frontend
frontend_dir = os.path.join(os.getcwd(), "frontend")
if os.path.exists(frontend_dir):
    app.mount("/", StaticFiles(directory=frontend_dir, html=True), name="frontend")


@app.on_event("startup")
def on_startup():
    """Startup event handler."""
    # Create database and tables
    create_db_and_tables()

    # Create necessary directories
    os.makedirs(settings.MEDIA_STORAGE_PATH, exist_ok=True)

    # 注册转换器（使用动态配置）
    service_manager.register_converters()


@app.get("/")
def read_root():
    """Root endpoint."""
    return {"message": "Welcome to TransMediaX API"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
