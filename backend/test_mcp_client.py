#!/usr/bin/env python3
"""
MCP 客户端测试脚本

该脚本用于测试 TransMediaX MCP 服务器的功能。

使用方法:
    # 测试 STDIO 模式的服务器
    python test_mcp_client.py --transport stdio --server-script mcp_server.py

    # 测试 SSE 模式的服务器
    python test_mcp_client.py --transport sse --url http://127.0.0.1:8001/mcp

    # 测试内存模式（直接连接到服务器实例）
    python test_mcp_client.py --transport memory
"""

import argparse
import asyncio
import logging
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastmcp import Client
from app.config import settings
from app.core.services.manager import service_manager
from app.core.mcp.server import initialize_mcp_server
from app.database import create_db_and_tables

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


async def test_server_info(client: Client):
    """测试服务器信息工具"""
    logger.info("=== 测试服务器信息 ===")

    try:
        # 获取服务器信息
        result = await client.call_tool("get_server_info", {})
        if hasattr(result, 'text'):
            logger.info(f"服务器信息: {result.text}")
        else:
            logger.info(f"服务器信息: {result}")

        # 健康检查
        result = await client.call_tool("health_check", {})
        if hasattr(result, 'text'):
            logger.info(f"健康检查: {result.text}")
        else:
            logger.info(f"健康检查: {result}")

    except Exception as e:
        logger.error(f"测试服务器信息失败: {e}")


async def test_converter_tools(client: Client):
    """测试转换器工具"""
    logger.info("=== 测试转换器工具 ===")

    try:
        # 列出可用转换器
        result = await client.call_tool("list_available_converters", {})
        if hasattr(result, 'text'):
            logger.info(f"可用转换器: {result.text}")
        else:
            logger.info(f"可用转换器: {result}")

        # 获取工具列表
        tools = await client.list_tools()
        logger.info(f"可用工具数量: {len(tools)}")

        converter_tools = [tool for tool in tools if tool.name.startswith("convert_")]
        logger.info(f"转换器工具: {[tool.name for tool in converter_tools]}")

        # 测试文本到图像转换（如果可用）
        if any(tool.name == "convert_text_to_image" for tool in converter_tools):
            logger.info("测试文本到图像转换...")
            result = await client.call_tool("convert_text_to_image", {
                "input_content": "一只可爱的小猫",
                "parameters": {
                    "width": 512,
                    "height": 512,
                    "num_images": 1
                }
            })
            if hasattr(result, 'text'):
                logger.info(f"文本到图像转换结果: {result.text}")
            else:
                logger.info(f"文本到图像转换结果: {result}")

        # 测试文本到音频转换（如果可用）
        if any(tool.name == "convert_text_to_audio" for tool in converter_tools):
            logger.info("测试文本到音频转换...")
            result = await client.call_tool("convert_text_to_audio", {
                "input_content": "你好，这是一个测试音频。",
                "parameters": {
                    "voice": "alloy",
                    "speed": 1.0
                }
            })
            if hasattr(result, 'text'):
                logger.info(f"文本到音频转换结果: {result.text}")
            else:
                logger.info(f"文本到音频转换结果: {result}")

    except Exception as e:
        logger.error(f"测试转换器工具失败: {e}")


async def test_memory_mode():
    """测试内存模式连接"""
    logger.info("=== 测试内存模式连接 ===")

    try:
        # 设置环境
        create_db_and_tables()
        os.makedirs(settings.MEDIA_STORAGE_PATH, exist_ok=True)
        service_manager.register_converters()

        # 初始化 MCP 服务器
        mcp_server = initialize_mcp_server("Test MCP Server")

        # 使用内存传输连接
        async with Client(mcp_server.get_fastmcp_instance()) as client:
            await test_server_info(client)
            await test_converter_tools(client)

    except Exception as e:
        logger.exception(f"内存模式测试失败: {e}")


async def test_remote_mode(url: str):
    """测试远程模式连接"""
    logger.info(f"=== 测试远程模式连接: {url} ===")

    try:
        async with Client(url) as client:
            await test_server_info(client)
            await test_converter_tools(client)

    except Exception as e:
        logger.exception(f"远程模式测试失败: {e}")


async def test_stdio_mode(server_script: str):
    """测试 STDIO 模式连接"""
    logger.info(f"=== 测试 STDIO 模式连接: {server_script} ===")

    try:
        async with Client(server_script) as client:
            await test_server_info(client)
            await test_converter_tools(client)

    except Exception as e:
        logger.exception(f"STDIO 模式测试失败: {e}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="TransMediaX MCP 客户端测试",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )

    parser.add_argument(
        "--transport",
        choices=["memory", "sse", "stdio"],
        default="memory",
        help="传输方式 (默认: memory)"
    )

    parser.add_argument(
        "--url",
        help="远程服务器 URL (用于 sse 模式)"
    )

    parser.add_argument(
        "--server-script",
        default="mcp_server.py",
        help="服务器脚本路径 (用于 stdio 模式)"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细日志输出"
    )

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    logger.info("开始 MCP 客户端测试")

    try:
        if args.transport == "memory":
            await test_memory_mode()
        elif args.transport == "sse":
            if not args.url:
                logger.error("SSE 模式需要指定 --url 参数")
                sys.exit(1)
            await test_remote_mode(args.url)
        elif args.transport == "stdio":
            await test_stdio_mode(args.server_script)

        logger.info("MCP 客户端测试完成")

    except Exception as e:
        logger.exception(f"测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
