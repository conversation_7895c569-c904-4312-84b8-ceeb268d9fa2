import os
import secrets
from typing import Optional, Dict, Any, List
from pydantic_settings import BaseSettings
from pydantic import validator
import json


class Settings(BaseSettings):
    """Application settings."""

    # Base settings
    PROJECT_NAME: str = "TransMediaX"
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)

    # Database settings
    DATABASE_PATH: str = "data/database.sqlite"
    SQL_ECHO: bool = False

    # CORS settings
    BACKEND_CORS_ORIGINS: List[str] = ["*"]

    # File storage settings
    MEDIA_STORAGE_PATH: str = "data/media"

    # Authentication settings
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days

    # Admin settings
    ADMIN_USERNAME: str = "admin"
    ADMIN_PASSWORD: str = "admin"  # Change this in production

    # Service settings
    # Speech to Text service (OpenAI compatible)
    STT_API_KEY: Optional[str] = None
    STT_BASE_URL: str = "https://api.openai.com/v1"
    STT_MODEL: str = "whisper-1"

    # Text to Speech service (OpenAI compatible)
    TTS_API_KEY: Optional[str] = None
    TTS_BASE_URL: str = "https://api.openai.com/v1"
    TTS_MODEL: str = "tts-1"

    # LLM service (OpenAI compatible)
    LLM_API_KEY: Optional[str] = None
    LLM_BASE_URL: str = "https://api.openai.com/v1"
    LLM_MODEL: str = "gpt-3.5-turbo"

    # 兼容旧版本配置
    AUDIO_API_KEY: Optional[str] = None
    AUDIO_BASE_URL: str = "https://api.openai.com/v1"

    # Image generation service
    COMFYUI_BASE_URL: str = "http://127.0.0.1:8188"

    # Config file path
    CONFIG_FILE_PATH: str = "data/config.json"

    # MCP Server settings
    MCP_ENABLED: bool = True
    MCP_SERVER_NAME: str = "TransMediaX MCP Server"
    MCP_TRANSPORT: str = "sse"  # stdio, sse, streamable-http
    MCP_HOST: str = "127.0.0.1"
    MCP_PORT: int = 8001
    MCP_PATH: str = "/mcp"
    MCP_MONITORING_ENABLED: bool = True
    MCP_MONITORING_INTERVAL: int = 30  # seconds

    # Global Converter Configuration (used by both API and MCP)
    # Text-to-Speech (TTS) Settings
    TTS_AVAILABLE_VOICES: List[str] = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
    TTS_AVAILABLE_MODELS: List[str] = ["F5-TTS"]
    TTS_AVAILABLE_FORMATS: List[str] = ["mp3", "opus", "aac", "flac"]
    TTS_DEFAULT_VOICE: str = "alloy"
    TTS_DEFAULT_MODEL: str = "F5-TTS"
    TTS_DEFAULT_FORMAT: str = "mp3"
    TTS_SPEED_MIN: float = 0.25
    TTS_SPEED_MAX: float = 4.0
    TTS_SPEED_DEFAULT: float = 1.0

    # Speech-to-Text (STT) Settings
    STT_AVAILABLE_MODELS: List[str] = ["SenseVoiceSmall"]
    STT_AVAILABLE_RESPONSE_FORMATS: List[str] = ["json", "text", "srt", "verbose_json", "vtt"]
    STT_DEFAULT_MODEL: str = "SenseVoiceSmall"
    STT_DEFAULT_RESPONSE_FORMAT: str = "text"
    STT_TEMPERATURE_MIN: float = 0.0
    STT_TEMPERATURE_MAX: float = 1.0
    STT_TEMPERATURE_DEFAULT: float = 0.0

    # Image Generation Settings
    IMAGE_SIZE_MIN: int = 64
    IMAGE_SIZE_MAX: int = 2048
    IMAGE_SIZE_DEFAULT: int = 512
    IMAGE_COUNT_MAX: int = 4
    IMAGE_COUNT_DEFAULT: int = 1
    IMAGE_CFG_SCALE_MIN: float = 1.0
    IMAGE_CFG_SCALE_MAX: float = 20.0
    IMAGE_CFG_SCALE_DEFAULT: float = 7.5
    IMAGE_STEPS_MIN: int = 1
    IMAGE_STEPS_MAX: int = 100
    IMAGE_STEPS_DEFAULT: int = 20

    @validator("DATABASE_PATH", pre=True)
    def assemble_db_path(cls, v: Optional[str], values: Dict[str, Any]) -> str:
        """Assemble database path."""
        if v and os.path.isabs(v):
            return v
        return os.path.join(os.getcwd(), v or "data/database.sqlite")

    @validator("MEDIA_STORAGE_PATH", pre=True)
    def assemble_media_path(cls, v: Optional[str], values: Dict[str, Any]) -> str:
        """Assemble media storage path."""
        if v and os.path.isabs(v):
            return v
        return os.path.join(os.getcwd(), v or "data/media")

    def load_from_file(self):
        """Load settings from config file."""
        if not os.path.exists(self.CONFIG_FILE_PATH):
            return

        try:
            with open(self.CONFIG_FILE_PATH, "r") as f:
                config_data = json.load(f)

            for key, value in config_data.items():
                if hasattr(self, key):
                    setattr(self, key, value)
        except Exception as e:
            print(f"Error loading config file: {e}")

    def save_to_file(self):
        """Save settings to config file."""
        # Ensure the directory exists
        os.makedirs(os.path.dirname(self.CONFIG_FILE_PATH), exist_ok=True)

        # Get all settings as dict
        settings_dict = self.dict()

        # Remove sensitive settings
        settings_dict.pop("SECRET_KEY", None)

        try:
            with open(self.CONFIG_FILE_PATH, "w") as f:
                json.dump(settings_dict, f, indent=2)
        except Exception as e:
            print(f"Error saving config file: {e}")


# Create settings instance
settings = Settings()

# Load settings from file
settings.load_from_file()
